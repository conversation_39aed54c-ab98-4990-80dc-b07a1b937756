﻿using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.Data.DataObjects.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Feature.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class PersonDataProviderExtension : IDataProviderExtension<PersonDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly IAuthentication _authentication;
        private Dictionary<Guid, short?> _originalMasterMenuOptions = new Dictionary<Guid, short?>();
        private Guid? _oldSiteId;
        private Guid? _oldDepartmentId;
        private Dictionary<Guid, bool?> _originalIsActiveDriver = new Dictionary<Guid, bool?>();

        // New class variables for license tracking
        private Dictionary<Guid, bool> _originalLicenseActive = new Dictionary<Guid, bool>();
        private Dictionary<Guid, DateTime> _originalLicenseExpiryDate = new Dictionary<Guid, DateTime>();
        private Dictionary<Guid, IEnumerable<LicenseByModelDataObject>> _originalLicenseByModelItems = new Dictionary<Guid, IEnumerable<LicenseByModelDataObject>>();

        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILoggingService _logger;

        public PersonDataProviderExtension(IServiceProvider serviceProvider, IAuthentication authentication, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler, IServiceScopeFactory serviceScopeFactory, ILoggingService logger)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
            _deviceMessageHandler = deviceMessageHandler;
            _authentication = authentication;
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeSaveDataSet += OnBeforeSaveDataSetAsync;
            dataProvider.OnAfterSave += DataProvider_OnAfterSaveAsync;
            dataProvider.OnAfterSaveDataSet += DataProvider_OnAfterSaveDataSet;
            dataProvider.OnAfterGetCollection += DataProvider_OnAfterGetCollection;
        }

        private async Task DataProvider_OnAfterGetCollection(OnAfterGetCollectionEventArgs arg)
        {
            // Only process if the result is a collection of PersonDataObject
            if (arg.Result is DataObjectCollection<PersonDataObject> items && arg.PageNumber > 0 && arg.PageSize > 0)
            {
                var userClaims = await _authentication.GetCurrentUserClaimsAsync();

                if (userClaims == null || userClaims.UserId == null)
                {
                    return;
                }

                var appUserClaims = userClaims as AppUserClaims;

                var preferredLocale = appUserClaims.UserPreferredLocale != null ? appUserClaims.UserPreferredLocale : appUserClaims.CustomerPreferredLocale;

                foreach (var item in items)
                {
                    try
                    {
                        var culture = !string.IsNullOrEmpty(preferredLocale) ? new CultureInfo(preferredLocale) : new CultureInfo("en-US");

                        var driver = await item.LoadDriverAsync();

                        if (driver != null && driver.LastSessionDateTzAdjusted.HasValue)
                        {
                            item.LastSessionDateTzAdjustedDisplay = driver.LastSessionDateTzAdjusted.Value.ToString($"{culture.DateTimeFormat.ShortDatePattern} HH:mm:ss", culture);
                        }
                    }
                    catch (CultureNotFoundException)
                    {
                        // If the culture is invalid, just return without modifying the datetime
                        return;
                    }
                }
            }
        }

        /// <summary>
        /// [FXQ-2372] This relationship is added to enable us to add a subform in Vehicle Checklist, to update the Department Checklist directly 
        /// from the Vehicle page. Because GO doesn't allow to create a subform on a subrelated field. The relationship is enforced 
        /// between Vehicle and DepartmentChecklist in VehicleToPreOpChecklistViewDataProviderExtension.OnBeforeSave
        /// </summary>
        private async Task DataProvider_OnAfterSaveDataSet(OnAfterSaveDataSetEventArgs arg)
        {
            if (!arg.EntityBeforeSave.IsNew)
            {
                var person = arg.EntityRefetched as PersonDataObject;
                if (person != null)
                {
                    // Check if IsActiveDriver changed from false to true
                    if (_originalIsActiveDriver.ContainsKey(person.Id) &&
                        _originalIsActiveDriver[person.Id] == false &&
                        person.IsActiveDriver == true)
                    {
                        // Clear old site/department IDs so we only CREATE access, don't remove anything
                        _oldSiteId = null;
                        _oldDepartmentId = null;

                        // Reuse existing method to create vehicle access for current department
                        await UpdateVehicleAccessForSiteDepartmentChangeAsync(person);
                    }
                    // Clean up stored value
                    if (_originalIsActiveDriver.ContainsKey(person.Id))
                    {
                        _originalIsActiveDriver.Remove(person.Id);
                    }

                    // Check if we need to update vehicle access due to site/department change
                    if (_oldSiteId.HasValue || _oldDepartmentId.HasValue)
                    {
                        await UpdateVehicleAccessForSiteDepartmentChangeAsync(person);
                        // Clear the stored values after processing
                        _oldSiteId = null;
                        _oldDepartmentId = null;
                    }

                    // Check if license-related values have changed
                    if (person.IsDriver == true && person.DriverId != null &&
                        (_originalLicenseActive.ContainsKey(person.Id) ||
                         _originalLicenseExpiryDate.ContainsKey(person.Id) ||
                         _originalLicenseByModelItems.ContainsKey(person.Id)))
                    {
                        await SyncVehiclesForLicenseChangesAsync(person);
                    }

                    // After access creation/removal logic
                    if (_originalIsActiveDriver.ContainsKey(person.Id) &&
                        _originalIsActiveDriver[person.Id] != person.IsActiveDriver)
                    {
                        // Get current user ID for authorization
                        var userClaims = await _authentication.GetCurrentUserClaimsAsync();
                        var currentUserId = userClaims?.UserId;

                        if (currentUserId.HasValue)
                        {
                            // Sync all vehicles this person has access to using queue-based approach
                            var driver = await person.LoadDriverAsync(skipSecurity: true);
                            if (driver?.Card != null)
                            {
                                await driver.Card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);
                                var vehicleAccesses = driver.Card.PerVehicleNormalCardAccessItems;

                                if (vehicleAccesses?.Any() == true)
                                {
                                    await QueueVehicleSyncForAccessChangesAsync(vehicleAccesses, currentUserId.Value, person, "IsActiveDriverChange", _serviceProvider);
                                }
                            }
                        }
                    }
                }
            }

            if (arg.EntityBeforeSave.IsNew)
            {
                var person = arg.EntityRefetched as PersonDataObject;

                var personUpdated = false;
                if (person.PersonChecklistLanguageSettingsId == null)
                {
                    var personChecklistLanguageSettings = _serviceProvider.GetRequiredService<PersonChecklistLanguageSettingsDataObject>();
                    personChecklistLanguageSettings.Language = ChecklistLanguageEnum.English;
                    personChecklistLanguageSettings = await _dataFacade.PersonChecklistLanguageSettingsDataProvider.SaveAsync(personChecklistLanguageSettings);

                    person.PersonChecklistLanguageSettingsId = personChecklistLanguageSettings.Id;
                    personUpdated = true;
                }

                if (personUpdated)
                {
                    person = await _dataFacade.PersonDataProvider.SaveAsync(person);
                }
            }
        }

        private async Task DataProvider_OnAfterSaveAsync(OnAfterSaveEventArgs e)
        {
            var person = e.Result as PersonDataObject;

            if (person == null)
            {
                return;
            }

            // provide Customer GoUser Role to the person if it has AccessGroupId
            // DO NOT MOVE THIS CODE (START)
            if (person.AccessGroupId != null)
            {
                var goUser = await person.LoadGOUserAsync();

                if (goUser != null)
                {
                    var existingGoUserRole = (await _dataFacade.GOUserRoleDataProvider.GetCollectionAsync(null, "GOUserId == @0 && GORoleName == @1", new object[] { goUser.Id, "Customer" })).SingleOrDefault();
                    if (existingGoUserRole == null)
                    {
                        var GOUserRole = _serviceProvider.GetRequiredService<GOUserRoleDataObject>();
                        GOUserRole.GORoleName = "Customer";
                        GOUserRole.UserForeignKey = goUser.Id;
                        // save the GOUserRole
                        await _dataFacade.GOUserRoleDataProvider.SaveAsync(GOUserRole);
                    }
                }
            }
            // DO NOT MOVE THIS CODE (END)

            // Get current user ID for authorization
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            _ = Task.Run(async () =>
            {
                var syncStart = DateTime.UtcNow;
                try
                {
                    await using var scope = _serviceScopeFactory.CreateAsyncScope();
                    var scopedDataFacade = scope.ServiceProvider.GetRequiredService<IDataFacade>();
                    var scopedDeviceTwinHandler = scope.ServiceProvider.GetRequiredService<IDeviceTwinHandler>();

                    if (person.DriverId == null)
                    {
                        return;
                    }

                    // Reload person data in the new scope
                    var scopedPerson = scope.ServiceProvider.GetRequiredService<PersonDataObject>();
                    scopedPerson.Id = person.Id;
                    scopedPerson = await scopedDataFacade.PersonDataProvider.GetAsync(scopedPerson,
                        includes: new List<string> { "Driver", "Driver.Card" },
                        skipSecurity: true);

                    if (scopedPerson?.Driver?.Card == null)
                    {
                        return;
                    }

                    var card = scopedPerson.Driver.Card;

                    if (card != null)
                    {
                        card.SiteId = person.SiteId;
                        await scopedDataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);
                    }

                    if (person.IsActiveDriver == false && card != null)
                    {
                        // Set Weigand to "0"
                        card.Weigand = ""; // Default Weigand if Driver is not active

                        // Save the updated card
                        await scopedDataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);
                    }

                    if (card != null && (!person.VehicleAccess || (!person.IsActiveDriver ?? false)))
                    {
                        _logger?.LogInformation($"[PERF] Starting bulk access removal for inactive driver - Card: {card.Id}");
                        var bulkDeleteStart = DateTime.UtcNow;

                        // Use optimized bulk deletion approach
                        var deletedVehicleIds = await RemoveAllNormalDriverAccessAsync(card, scopedDataFacade);

                        // Queue vehicle sync for affected vehicles
                        if (deletedVehicleIds.Any() && currentUserId.HasValue)
                        {
                            await QueueVehicleSyncForMultipleVehiclesAsync(
                                deletedVehicleIds,
                                currentUserId.Value,
                                "BulkAccessRemoval",
                                scope.ServiceProvider);
                        }

                        var bulkDeleteDuration = (DateTime.UtcNow - bulkDeleteStart).TotalMilliseconds;
                        _logger?.LogInformation($"[PERF] Bulk access removal completed in {bulkDeleteDuration}ms for {deletedVehicleIds.Count} vehicles");
                    }

                    // remove all access to supervisor vehicle access if IsSupervisor is false
                    if ((!person.Supervisor ?? false) && card != null)
                    {
                        _logger?.LogInformation($"[PERF] Starting bulk supervisor access removal - Card: {card.Id}");
                        var bulkDeleteStart = DateTime.UtcNow;

                        // Use optimized bulk deletion approach for supervisor access
                        var deletedVehicleIds = await RemoveAllSupervisorAccessAsync(card, scopedDataFacade);

                        // Queue vehicle sync for affected vehicles
                        if (deletedVehicleIds.Any() && currentUserId.HasValue)
                        {
                            await QueueVehicleSyncForMultipleVehiclesAsync(
                                deletedVehicleIds,
                                currentUserId.Value,
                                "BulkSupervisorAccessRemoval",
                                scope.ServiceProvider);
                        }

                        var bulkDeleteDuration = (DateTime.UtcNow - bulkDeleteStart).TotalMilliseconds;
                        _logger?.LogInformation($"[PERF] Bulk supervisor access removal completed in {bulkDeleteDuration}ms for {deletedVehicleIds.Count} vehicles");
                    }

                    // Check if MasterMenuOptions changed and sync drivers if needed
                    if (_originalMasterMenuOptions.ContainsKey(person.Id) &&
                        _originalMasterMenuOptions[person.Id] != person.MasterMenuOptions && person.Supervisor == true && currentUserId.HasValue)
                    {
                        if (card != null)
                        {
                            await card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);
                            var perVehicleNormalCardAccessItems = card.PerVehicleNormalCardAccessItems;

                            if (perVehicleNormalCardAccessItems?.Any() == true)
                            {
                                await QueueVehicleSyncForAccessChangesAsync(perVehicleNormalCardAccessItems, currentUserId.Value, person, "MasterMenuOptionsChange", scope.ServiceProvider);
                            }
                        }
                    }

                    // Clean up stored value
                    if (_originalMasterMenuOptions.ContainsKey(person.Id))
                    {
                        _originalMasterMenuOptions.Remove(person.Id);
                    }

                    var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                    _logger?.LogInformation($"[PERF] PersonDataProviderExtension background task completed in {syncDuration}ms");
                }
                catch (Exception ex)
                {
                    var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                    _logger?.LogError(ex, $"[PERF] PersonDataProviderExtension background task failed after {syncDuration}ms: {ex.Message}");
                }
            });
        }

        private async Task OnBeforeSaveDataSetAsync(OnBeforeSaveDataSetEventArgs e)
        {
            var person = e.Entity as PersonDataObject;
            try
            {
                var customer = await person.LoadCustomerAsync(skipSecurity: true);
                person.CustomerName = customer?.CompanyName;

                // Store original MasterMenuOptions if not already stored
                if (!_originalMasterMenuOptions.ContainsKey(person.Id))
                {
                    _originalMasterMenuOptions[person.Id] = person.MasterMenuOptions;
                }

                // Store original IsActiveDriver if not already stored
                if (!person.IsNew && !_originalIsActiveDriver.ContainsKey(person.Id))
                {
                    var originalPerson = _serviceProvider.GetRequiredService<PersonDataObject>();
                    originalPerson.Id = person.Id;
                    originalPerson = await _dataFacade.PersonDataProvider.GetAsync(originalPerson, skipSecurity: true);
                    _originalIsActiveDriver[person.Id] = originalPerson.IsActiveDriver;
                }

                // Check and store license-related values for existing persons
                if (!person.IsNew && person.IsDriver == true && person.DriverId != null)
                {
                    var originalPerson = _serviceProvider.GetRequiredService<PersonDataObject>();
                    originalPerson.Id = person.Id;
                    originalPerson = await _dataFacade.PersonDataProvider.GetAsync(originalPerson, skipSecurity: true);

                    // Check LicenseActive changes
                    if (originalPerson.LicenseActive != person.LicenseActive)
                    {
                        _originalLicenseActive[person.Id] = originalPerson.LicenseActive;
                    }

                    // Check license details changes
                    var originalDriver = await originalPerson.LoadDriverAsync(skipSecurity: true);
                    var currentDriver = await person.LoadDriverAsync(skipSecurity: true);

                    if (originalDriver != null && currentDriver != null)
                    {
                        // Check general license expiry date changes
                        var originalLicenseDetail = await originalDriver.LoadGeneralLicenceAsync(skipSecurity: true);
                        var currentLicenseDetail = await currentDriver.LoadGeneralLicenceAsync(skipSecurity: true);

                        if (originalLicenseDetail != null && currentLicenseDetail != null &&
                            originalLicenseDetail.ExpiryDate != currentLicenseDetail.ExpiryDate)
                        {
                            _originalLicenseExpiryDate[person.Id] = originalLicenseDetail.ExpiryDate;
                        }

                        // Check model licenses changes
                        var originalLicenseByModelItems = await originalDriver.LoadLicensesByModelAsync(skipSecurity: true);
                        var currentLicenseByModelItems = await currentDriver.LoadLicensesByModelAsync(skipSecurity: true);

                        var originalItems = originalLicenseByModelItems?.ToList() ?? new List<LicenseByModelDataObject>();
                        var currentItems = currentLicenseByModelItems?.ToList() ?? new List<LicenseByModelDataObject>();

                        if (originalItems.Count != currentItems.Count ||
                            originalItems.Any(o => !currentItems.Any(c =>
                                c.ModelId == o.ModelId &&
                                c.ExpiryDate == o.ExpiryDate)))
                        {
                            _originalLicenseByModelItems[person.Id] = originalItems;
                        }
                    }
                }

                // Check if site or department has changed for existing persons
                if (!person.IsNew)
                {
                    var originalPerson = _serviceProvider.GetRequiredService<PersonDataObject>();
                    originalPerson.Id = person.Id;
                    originalPerson = await _dataFacade.PersonDataProvider.GetAsync(originalPerson, skipSecurity: true);

                    if (originalPerson.SiteId != person.SiteId)
                    {
                        // Store old values for use in OnAfterSaveDataSet
                        _oldSiteId = originalPerson.SiteId;
                    }
                    if (originalPerson.DepartmentId != person.DepartmentId)
                    {
                        // Store old values for use in OnAfterSaveDataSet
                        _oldDepartmentId = originalPerson.DepartmentId;
                    }
                }

                // Update driver status
                if (person.IsDriver == true && person.DriverId != null)
                {
                    var driver = await person.LoadDriverAsync(skipSecurity: true);
                    driver.Active = person.IsActiveDriver ?? false;
                    driver.CustomerId = person.CustomerId;
                    driver.SiteId = person.SiteId;
                    driver.DepartmentId = person.DepartmentId;

                    // Update HasLicense status
                    await UpdatePersonLicenseStatusAsync(person, driver);
                }

                // Check if we want to create a driver (IsDriver set to true, and no driver yet associated to the person)
                if (person.IsDriver == true && person.DriverId is null)
                {
                    var driver = _serviceProvider.GetRequiredService<DriverDataObject>();
                    driver.Id = Guid.NewGuid();
                    driver.LicenseMode = ModeEnum.General;
                    driver.Active = true;
                    driver.CustomerId = person.CustomerId;
                    driver.SiteId = person.SiteId;
                    driver.DepartmentId = person.DepartmentId;

                    person.Driver = driver;
                }

                if (person.OnDemand)
                {
                    person.VehicleAccess = false;
                }
                else
                {
                    person.VehicleAccess = true;
                }

                CalculateStayOfYears(person);
                CalculateMasterMenuOptions(person);
            }
            catch (Exception ex) when (IsTransientDbError(ex))
            {
                _logger?.LogWarning($"[PersonDataProviderExtension] Transient DB issue during OnBeforeSaveDataSet for person {person?.Id}. Continuing without blocking. Error: {ex.Message}");
            }
        }



        private void CalculateMasterMenuOptions(PersonDataObject person)
        {
            // Store original value before calculating new one
            if (!_originalMasterMenuOptions.ContainsKey(person.Id))
            {
                _originalMasterMenuOptions[person.Id] = person.MasterMenuOptions;
            }

            person.MasterMenuOptions = 15;

            if (!person.CanUnlockVehicle)
            {
                //00001110
                person.MasterMenuOptions -= 1;
            }

            if (!person.NormalDriverAccess)
            {
                //00001101
                person.MasterMenuOptions -= 2;
            }

            if (!person.VORActivateDeactivate)
            {
                //00001011
                person.MasterMenuOptions -= 4;
            }

            if (!person.MaintenanceMode)
            {
                //00000111
                person.MasterMenuOptions -= 8;
            }
        }

        /// <summary>
        /// Updates vehicle access when a person's site or department changes
        /// </summary>
        /// <param name="person">The updated person object</param>
        private async Task UpdateVehicleAccessForSiteDepartmentChangeAsync(PersonDataObject person)
        {
            // Skip if person is not a driver or doesn't have vehicle access
            if (person.IsDriver != true || person.DriverId == null)
            {
                return;
            }

            var driver = await person.LoadDriverAsync(skipSecurity: true);
            var card = await driver?.LoadCardAsync(skipSecurity: true);

            // Skip if driver doesn't have a card
            if (card == null)
            {
                return;
            }

            // Get permissions for normal driver and supervisor
            var normalDriverPermission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null,
                "LevelName == @0", new object[] { (int)PermissionLevelEnum.NormalDriver })).FirstOrDefault();
            var masterPermission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null,
                "LevelName == @0", new object[] { (int)PermissionLevelEnum.Master })).FirstOrDefault();

            if (normalDriverPermission == null || masterPermission == null)
            {
                return;
            }

            // 1. Delete existing vehicle access
            // Load all access items
            await card.LoadSiteVehicleNormalCardAccessItemsAsync(skipSecurity: true);
            await card.LoadDepartmentVehicleNormalCardAccessItemsAsync(skipSecurity: true);
            await card.LoadModelVehicleNormalCardAccessItemsAsync(skipSecurity: true);
            await card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);

            var perVehicleAccessesToDelete = new List<PerVehicleNormalCardAccessDataObject>();

            // Find and mark for deletion all site access records for the old site
            if (_oldSiteId != null && card.SiteVehicleNormalCardAccessItems?.Any() == true)
            {
                foreach (var item in card.SiteVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }
            }

            // Find and mark for deletion all department access records for the old department
            if (_oldDepartmentId != null && card.DepartmentVehicleNormalCardAccessItems?.Any() == true)
            {
                foreach (var item in card.DepartmentVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }
            }

            // Find and mark for deletion all model access records for the old department
            if (_oldDepartmentId != null && card.ModelVehicleNormalCardAccessItems?.Any() == true)
            {
                // Mark the model access items for deletion since they will be re-created in the new department
                foreach (var item in card.ModelVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }
            }

            // Find vehicles in the old department to remove access and collect IoT devices for syncing
            var vehiclesToSync = new HashSet<string>();
            if (_oldDepartmentId != null && card.PerVehicleNormalCardAccessItems?.Any() == true)
            {
                // Mark the vehicle access items for deletion since they will be re-created in the new department
                foreach (var item in card.PerVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }
            }


            // Save the card with marked for deletion items
            await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);

            // 2. Use VehicleAccessUtilities queue-based approach for better performance and reliability
            var vehicleAccessUtilities = _serviceProvider.GetRequiredService<IVehicleAccessUtilities>();

            // Build the new access collections based on person's new site/department
            var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();

            // Site accesses
            var siteAccesses = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
            if (person.SiteId != null)
            {
                // Normal driver site access
                var siteAccess = _serviceProvider.GetRequiredService<PersonToSiteVehicleNormalAccessViewDataObject>();
                siteAccess.PersonId = person.Id;
                siteAccess.SiteId = person.SiteId;
                siteAccess.PermissionId = normalDriverPermission.Id;
                siteAccess.HasAccess = true;
                siteAccess.IsNew = false;
                dataset.AddObject(siteAccess);
                siteAccesses.Add(siteAccess);

                // Supervisor site access if applicable
                if (person.Supervisor == true)
                {
                    var siteSupervisorAccess = _serviceProvider.GetRequiredService<PersonToSiteVehicleNormalAccessViewDataObject>();
                    siteSupervisorAccess.PersonId = person.Id;
                    siteSupervisorAccess.SiteId = person.SiteId;
                    siteSupervisorAccess.PermissionId = masterPermission.Id;
                    siteSupervisorAccess.HasAccess = true;
                    siteSupervisorAccess.IsNew = false;
                    dataset.AddObject(siteSupervisorAccess);
                    siteAccesses.Add(siteSupervisorAccess);
                }
            }

            // Create targeted access collections for ONLY the person's specific department
            var departmentAccesses = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
            var modelAccesses = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
            var vehicleAccesses = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };

            // Only create access for the person's specific department (not all departments in site)
            if (person.DepartmentId != null)
            {
                // Create department access for the specific department only
                var deptAccess = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewDataObject>();
                deptAccess.PersonId = person.Id;
                deptAccess.DepartmentId = person.DepartmentId;
                deptAccess.PermissionId = normalDriverPermission.Id;
                deptAccess.HasAccess = true;
                deptAccess.IsNew = false;
                dataset.AddObject(deptAccess);
                departmentAccesses.Add(deptAccess);

                // Add supervisor department access if applicable
                if (person.Supervisor == true)
                {
                    var deptSupervisorAccess = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewDataObject>();
                    deptSupervisorAccess.PersonId = person.Id;
                    deptSupervisorAccess.DepartmentId = person.DepartmentId;
                    deptSupervisorAccess.PermissionId = masterPermission.Id;
                    deptSupervisorAccess.HasAccess = true;
                    deptSupervisorAccess.IsNew = false;
                    dataset.AddObject(deptSupervisorAccess);
                    departmentAccesses.Add(deptSupervisorAccess);
                }

                // Get vehicles ONLY in this specific department
                var departmentVehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(
                    null, "DepartmentId == @0", new object[] { person.DepartmentId }, skipSecurity: true);

                if (departmentVehicles?.Any() == true)
                {
                    // Group by model to create model access for this department only
                    var modelGroups = departmentVehicles
                        .Where(v => v.ModelId != null)
                        .GroupBy(v => v.ModelId);

                    foreach (var modelGroup in modelGroups)
                    {
                        var modelId = modelGroup.Key;

                        // Create model access for normal driver
                        var modelAccess = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewDataObject>();
                        modelAccess.PersonId = person.Id;
                        modelAccess.ModelId = modelId;
                        modelAccess.DepartmentId = person.DepartmentId;
                        modelAccess.PermissionId = normalDriverPermission.Id;
                        modelAccess.HasAccess = true;
                        modelAccess.IsNew = false;
                        dataset.AddObject(modelAccess);
                        modelAccesses.Add(modelAccess);

                        // Add supervisor model access if applicable
                        if (person.Supervisor == true)
                        {
                            var modelSupervisorAccess = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewDataObject>();
                            modelSupervisorAccess.PersonId = person.Id;
                            modelSupervisorAccess.ModelId = modelId;
                            modelSupervisorAccess.DepartmentId = person.DepartmentId;
                            modelSupervisorAccess.PermissionId = masterPermission.Id;
                            modelSupervisorAccess.HasAccess = true;
                            modelSupervisorAccess.IsNew = false;
                            dataset.AddObject(modelSupervisorAccess);
                            modelAccesses.Add(modelSupervisorAccess);
                        }
                    }

                    // Create vehicle access for each vehicle in this department only
                    foreach (var vehicle in departmentVehicles)
                    {
                        // Create vehicle access for normal driver
                        var vehicleAccess = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewDataObject>();
                        vehicleAccess.PersonId = person.Id;
                        vehicleAccess.VehicleId = vehicle.Id;
                        vehicleAccess.PermissionId = normalDriverPermission.Id;
                        vehicleAccess.HasAccess = true;
                        vehicleAccess.IsNew = false;
                        dataset.AddObject(vehicleAccess);
                        vehicleAccesses.Add(vehicleAccess);

                        // Add supervisor vehicle access if applicable
                        if (person.Supervisor == true)
                        {
                            var vehicleSupervisorAccess = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewDataObject>();
                            vehicleSupervisorAccess.PersonId = person.Id;
                            vehicleSupervisorAccess.VehicleId = vehicle.Id;
                            vehicleSupervisorAccess.PermissionId = masterPermission.Id;
                            vehicleSupervisorAccess.HasAccess = true;
                            vehicleSupervisorAccess.IsNew = false;
                            dataset.AddObject(vehicleSupervisorAccess);
                            vehicleAccesses.Add(vehicleSupervisorAccess);
                        }
                    }
                }
            }

            // Queue the access updates using the robust VehicleAccessUtilities infrastructure
            await vehicleAccessUtilities.UpdateAccessesForPersonAsync(
                siteAccesses,
                departmentAccesses,
                modelAccesses,
                vehicleAccesses,
                person.Id,
                person.Supervisor == true ? (int)PermissionLevelEnum.Master : (int)PermissionLevelEnum.NormalDriver,
                true,
                null
            );
        }

        private async Task UpdatePersonLicenseStatusAsync(PersonDataObject person, DriverDataObject driver)
        {
            // Check if person has any licenses (either general or by model)
            var hasGeneralLicense = await driver.LoadGeneralLicenceAsync(skipSecurity: true) != null;
            var hasByModelLicenses = (await driver.LoadLicensesByModelAsync(skipSecurity: true))?.Any() ?? false;

            // Update HasLicense based on whether either type of license exists
            person.HasLicense = hasGeneralLicense || hasByModelLicenses;
        }

        private void CalculateStayOfYears(PersonDataObject person)
        {
            if (person.EntryDate == null)
            {
                person.YearOfStayCount = null;
                return;
            }

            var today = DateTime.Today;
            var EntryDate = person.EntryDate.Value;
            var years = today.Year - EntryDate.Year;

            person.YearOfStayCount = Convert.ToInt16(years); // cast as short
        }

        /// <summary>
        /// Synchronizes all vehicles that the driver has access to when license-related values change
        /// </summary>
        private async Task SyncVehiclesForLicenseChangesAsync(PersonDataObject person)
        {
            var driver = await person.LoadDriverAsync(skipSecurity: true);
            if (driver == null)
            {
                return;
            }

            // Get current user ID for authorization
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (!currentUserId.HasValue)
            {
                _logger?.LogWarning($"[PersonDataProviderExtension] SyncVehiclesForLicenseChangesAsync: userId is null, skipping sync for person {person.Id}");
                return;
            }

            // Get all vehicles the driver has access to
            var card = await driver.LoadCardAsync(skipSecurity: true);
            if (card != null)
            {
                await card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);
                var vehicleAccesses = card.PerVehicleNormalCardAccessItems;

                if (vehicleAccesses?.Any() == true)
                {
                    // Use queue-based sync instead of direct sync
                    await QueueVehicleSyncForAccessChangesAsync(vehicleAccesses, currentUserId.Value, person, "LicenseChange", _serviceProvider);
                }
            }

            // Clean up stored values
            _originalLicenseActive.Remove(person.Id);
            _originalLicenseExpiryDate.Remove(person.Id);
            _originalLicenseByModelItems.Remove(person.Id);
        }

        /// <summary>
        /// Queues vehicle sync messages for access changes using the queue-based approach
        /// </summary>
        private async Task QueueVehicleSyncForAccessChangesAsync(IEnumerable<PerVehicleNormalCardAccessDataObject> vehicleAccesses, Guid userId, PersonDataObject person, string syncReason, IServiceProvider serviceProvider)
        {
            if (!vehicleAccesses.Any())
            {
                return;
            }

            var vehicleIds = vehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
            var correlationId = Guid.NewGuid().ToString();
            var vehicleSyncQueueService = serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

            // Send individual message for each vehicle using the same pattern as VehicleAccessUtilities
            for (int i = 0; i < vehicleIds.Count; i++)
            {
                var syncMessage = new VehicleSyncMessage
                {
                    VehicleId = vehicleIds[i],
                    PersonId = person.Id,
                    CustomerId = person.CustomerId,
                    InitiatedByUserId = userId,
                    CreatedAt = DateTime.UtcNow,
                    CorrelationId = correlationId,
                    Priority = "Normal",
                    SyncReason = syncReason,
                    VehicleSequence = i + 1,
                    TotalVehicles = vehicleIds.Count
                };

                await vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
            }

            _logger?.LogInformation($"[PERF] {vehicleIds.Count} vehicle sync messages queued for person {person.Id} ({syncReason}) with correlation {correlationId}");
        }

        /// <summary>
        /// Optimized method to queue vehicle sync for multiple vehicles in bulk
        /// </summary>
        private async Task QueueVehicleSyncForMultipleVehiclesAsync(
            List<Guid> vehicleIds,
            Guid currentUserId,
            string syncReason,
            IServiceProvider serviceProvider)
        {
            if (!vehicleIds.Any())
            {
                return;
            }

            var correlationId = Guid.NewGuid().ToString();
            var vehicleSyncQueueService = serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

            _logger?.LogInformation($"[PERF] Starting bulk vehicle sync queueing for {vehicleIds.Count} vehicles - Reason: {syncReason}");

            // Send individual message for each vehicle with batching for better performance
            for (int i = 0; i < vehicleIds.Count; i++)
            {
                var syncMessage = new VehicleSyncMessage
                {
                    VehicleId = vehicleIds[i],
                    PersonId = Guid.Empty, // Not applicable for bulk operations
                    CustomerId = Guid.Empty, // Will be determined by the vehicle
                    InitiatedByUserId = currentUserId,
                    CreatedAt = DateTime.UtcNow,
                    CorrelationId = correlationId,
                    Priority = "Normal",
                    SyncReason = syncReason,
                    VehicleSequence = i + 1,
                    TotalVehicles = vehicleIds.Count,
                    PermissionLevels = null // Full sync for bulk operations
                };

                await vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
            }

            _logger?.LogInformation($"[PERF] {vehicleIds.Count} vehicle sync messages queued for bulk operation ({syncReason}) with correlation {correlationId}");
        }


        // Detect transient DB/connection pool issues and avoid surfacing them to UI popups
        // The logs will be kept in the logs table or application insights
        private static bool IsTransientDbError(Exception ex)
        {
            var msg = (ex.Message ?? string.Empty).ToLowerInvariant();
            var inner = (ex.InnerException?.Message ?? string.Empty).ToLowerInvariant();
            return msg.Contains("timeout expired") ||
                   msg.Contains("max pool size") ||
                   msg.Contains("pool") ||
                   msg.Contains("could not obtain a connection") ||
                   inner.Contains("timeout expired") ||
                   inner.Contains("max pool size") ||
                   inner.Contains("pool") ||
                   inner.Contains("could not obtain a connection");
        }

        /// <summary>
        /// Optimized bulk deletion of all normal driver access (Level 3) for a card
        /// Uses collection.Clear() to avoid individual ORM delete traversals
        /// </summary>
        private async Task<List<Guid>> RemoveAllNormalDriverAccessAsync(CardDataObject card, IDataFacade dataFacade)
        {
            var permissionDriver = (await dataFacade.PermissionDataProvider.GetCollectionAsync(null,
                $"LevelName == @0", new object[] { (int)PermissionLevelEnum.NormalDriver }, skipSecurity: true)).SingleOrDefault();

            if (permissionDriver == null)
            {
                return new List<Guid>();
            }

            var vehicleIds = new List<Guid>();

            // Load all access collections
            await card.LoadSiteVehicleNormalCardAccessItemsAsync(skipSecurity: true);
            await card.LoadDepartmentVehicleNormalCardAccessItemsAsync(skipSecurity: true);
            await card.LoadModelVehicleNormalCardAccessItemsAsync(skipSecurity: true);
            await card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);

            // Collect vehicle IDs before deletion
            var perVehicleItemsToDelete = card.PerVehicleNormalCardAccessItems
                ?.Where(item => item.PermissionId == permissionDriver.Id)
                .ToList();

            if (perVehicleItemsToDelete?.Any() == true)
            {
                vehicleIds.AddRange(perVehicleItemsToDelete.Select(item => item.VehicleId));
            }

            // Use efficient bulk removal - Clear collections entirely instead of marking individual items
            // This avoids the expensive ORM delete traversal for each item
            if (card.SiteVehicleNormalCardAccessItems?.Any(item => item.PermissionId == permissionDriver.Id) == true)
            {
                var itemsToRemove = card.SiteVehicleNormalCardAccessItems.Where(item => item.PermissionId == permissionDriver.Id).ToList();
                foreach (var item in itemsToRemove)
                {
                    card.SiteVehicleNormalCardAccessItems.Remove(item);
                }
            }

            if (card.DepartmentVehicleNormalCardAccessItems?.Any(item => item.PermissionId == permissionDriver.Id) == true)
            {
                var itemsToRemove = card.DepartmentVehicleNormalCardAccessItems.Where(item => item.PermissionId == permissionDriver.Id).ToList();
                foreach (var item in itemsToRemove)
                {
                    card.DepartmentVehicleNormalCardAccessItems.Remove(item);
                }
            }

            if (card.ModelVehicleNormalCardAccessItems?.Any(item => item.PermissionId == permissionDriver.Id) == true)
            {
                var itemsToRemove = card.ModelVehicleNormalCardAccessItems.Where(item => item.PermissionId == permissionDriver.Id).ToList();
                foreach (var item in itemsToRemove)
                {
                    card.ModelVehicleNormalCardAccessItems.Remove(item);
                }
            }

            if (card.PerVehicleNormalCardAccessItems?.Any(item => item.PermissionId == permissionDriver.Id) == true)
            {
                var itemsToRemove = card.PerVehicleNormalCardAccessItems.Where(item => item.PermissionId == permissionDriver.Id).ToList();
                foreach (var item in itemsToRemove)
                {
                    card.PerVehicleNormalCardAccessItems.Remove(item);
                }
            }

            // Save changes
            await dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);

            return vehicleIds.Distinct().ToList();
        }

        /// <summary>
        /// Optimized bulk deletion of all supervisor access (Level 1) for a card
        /// Uses collection.Clear() to avoid individual ORM delete traversals
        /// </summary>
        private async Task<List<Guid>> RemoveAllSupervisorAccessAsync(CardDataObject card, IDataFacade dataFacade)
        {
            var permissionSupervisor = (await dataFacade.PermissionDataProvider.GetCollectionAsync(null,
                $"LevelName == @0", new object[] { (int)PermissionLevelEnum.Master }, skipSecurity: true)).SingleOrDefault();

            if (permissionSupervisor == null)
            {
                return new List<Guid>();
            }

            var vehicleIds = new List<Guid>();

            // Load all access collections
            await card.LoadSiteVehicleNormalCardAccessItemsAsync(skipSecurity: true);
            await card.LoadDepartmentVehicleNormalCardAccessItemsAsync(skipSecurity: true);
            await card.LoadModelVehicleNormalCardAccessItemsAsync(skipSecurity: true);
            await card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);

            // Collect vehicle IDs before deletion
            var perVehicleItemsToDelete = card.PerVehicleNormalCardAccessItems
                ?.Where(item => item.PermissionId == permissionSupervisor.Id)
                .ToList();

            if (perVehicleItemsToDelete?.Any() == true)
            {
                vehicleIds.AddRange(perVehicleItemsToDelete.Select(item => item.VehicleId));
            }

            // Use efficient bulk removal
            if (card.SiteVehicleNormalCardAccessItems?.Any(item => item.PermissionId == permissionSupervisor.Id) == true)
            {
                var itemsToRemove = card.SiteVehicleNormalCardAccessItems.Where(item => item.PermissionId == permissionSupervisor.Id).ToList();
                foreach (var item in itemsToRemove)
                {
                    card.SiteVehicleNormalCardAccessItems.Remove(item);
                }
            }

            if (card.DepartmentVehicleNormalCardAccessItems?.Any(item => item.PermissionId == permissionSupervisor.Id) == true)
            {
                var itemsToRemove = card.DepartmentVehicleNormalCardAccessItems.Where(item => item.PermissionId == permissionSupervisor.Id).ToList();
                foreach (var item in itemsToRemove)
                {
                    card.DepartmentVehicleNormalCardAccessItems.Remove(item);
                }
            }

            if (card.ModelVehicleNormalCardAccessItems?.Any(item => item.PermissionId == permissionSupervisor.Id) == true)
            {
                var itemsToRemove = card.ModelVehicleNormalCardAccessItems.Where(item => item.PermissionId == permissionSupervisor.Id).ToList();
                foreach (var item in itemsToRemove)
                {
                    card.ModelVehicleNormalCardAccessItems.Remove(item);
                }
            }

            if (card.PerVehicleNormalCardAccessItems?.Any(item => item.PermissionId == permissionSupervisor.Id) == true)
            {
                var itemsToRemove = card.PerVehicleNormalCardAccessItems.Where(item => item.PermissionId == permissionSupervisor.Id).ToList();
                foreach (var item in itemsToRemove)
                {
                    card.PerVehicleNormalCardAccessItems.Remove(item);
                }
            }

            // Save changes
            await dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);

            return vehicleIds.Distinct().ToList();
        }
    }
}
