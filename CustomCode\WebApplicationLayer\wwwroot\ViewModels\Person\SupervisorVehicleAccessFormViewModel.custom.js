﻿(function () {
    FleetXQ.Web.ViewModels.SupervisorVehicleAccessFormViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        this.IsModifyCommandVisible = function () {
            return (self.viewmodel.StatusData.DisplayMode() == 'view' && !self.viewmodel.StatusData.IsEmpty() && self.viewmodel.DataStore && self.viewmodel.DataStore.CheckAuthorizationForEntityAndMethod('save'))
                && (ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EDIT_VEHICLE_ACCESS));
        }

        this.IsSaveCommandVisible = ko.pureComputed(function () {

            return false;
        });



        // Helper method to check if any access is being added (HasAccess changed from false to true)
        this.hasAccessBeingAdded = function (accesses) {
            if (!accesses || accesses.length === 0) return false;

            return accesses.some(function (access) {
                // Check if HasAccess is true and the object is dirty (indicating a change)
                return access.Data.HasAccess() && access.Data.IsDirty();
            });
        };

        // Method to perform the actual save operation
        this.PerformSave = function () {
            // SMART GUARD: Check if supervisor form should be active
            var canUnlockVehicle = self.viewmodel.CurrentObject().Data.CanUnlockVehicle();
            var normalDriverAccess = self.viewmodel.CurrentObject().Data.NormalDriverAccess();
            var vorActivateDeactivate = self.viewmodel.CurrentObject().Data.VORActivateDeactivate();
            var shouldSupervise = canUnlockVehicle || normalDriverAccess || vorActivateDeactivate;

            console.log("[SUPERVISOR FORM] PerformSave called - Should supervise:", shouldSupervise);

            if (!shouldSupervise) {
                console.log("[SUPERVISOR FORM] GUARD: Not supervisor user, skipping");
                return;
            }

            console.log("[SUPERVISOR FORM] PROCEEDING with PerformSave - Setting permissionLevel = 1");

            var configuration = {};
            configuration.caller = self.viewmodel;
            configuration.contextId = self.viewmodel.contextId;
            configuration.successHandler = self.onNewSaveSuccess;
            configuration.errorHandler = self.onNewSaveError;
            configuration.personId = self.viewmodel.PersonObject().Data.Id();
            // CRITICAL: Always pass PermissionLevel = 1 for supervisor access (bulk operations)
            configuration.permissionLevel = 1;

            // Get ALL site accesses (both checked and unchecked) from current view
            // This provides complete state information to the server for proper change tracking
            var siteAccesses = self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                .map(function (item) { return item.CurrentObject(); });
            configuration.personToSiteAccesses = self.getObjectsForSiteAccess(siteAccesses);

            // Get ALL department accesses (both checked and unchecked) from current view
            // This provides complete state information to the server for proper change tracking
            var departmentAccesses = self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                .map(function (item) { return item.CurrentObject(); });
            configuration.personToDepartmentAccesses = self.getObjectsForDepartmentAccess(departmentAccesses);

            // Get ALL model accesses (both checked and unchecked) from current view
            // This provides complete state information to the server for proper change tracking
            var modelAccesses = self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                .map(function (item) { return item.CurrentObject(); });
            configuration.personToModelAccesses = self.getObjectsForModelAccess(modelAccesses);

            // Get ALL vehicle accesses (both checked and unchecked) from current view
            // This provides complete state information to the server for proper change tracking
            var vehicleAccesses = self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                .map(function (item) { return item.CurrentObject(); });

            configuration.personToVehicleAccesses = self.getObjectsForVehicleAccess(vehicleAccesses);

            // Check if access is being added and prompt for cascade confirmation
            var hasAdditions = false;
            var currentTabData = null;
            var currentTab = self.viewmodel.StatusData.CurrentTabIndex();

            if (currentTab == 1) { // Sites tab
                currentTabData = self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                    .map(function (item) { return item.CurrentObject(); });
                hasAdditions = self.hasAccessBeingAdded(currentTabData);
            } else if (currentTab == 2) { // Departments tab
                currentTabData = self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                    .map(function (item) { return item.CurrentObject(); });
                hasAdditions = self.hasAccessBeingAdded(currentTabData);
            } else if (currentTab == 3) { // Models tab
                currentTabData = self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                    .map(function (item) { return item.CurrentObject(); });
                hasAdditions = self.hasAccessBeingAdded(currentTabData);
            }

            // If access is being added, prompt user for cascade confirmation
            if (hasAdditions && currentTab <= 3) { // Only prompt for Sites, Departments, and Models (not Vehicles)
                var tabName = currentTab == 1 ? "site" : (currentTab == 2 ? "department" : "model");
                var message = "You are adding " + tabName + " access. Do you want to automatically add access to all related child items?";

                self.viewmodel.controller.applicationController.showConfirmPopup(
                    self.viewmodel,
                    message,
                    "Cascade Access Permission",
                    function (cascadeConfirmed) {
                        configuration.cascadeAddPermission = cascadeConfirmed;
                        self.executeSupervisorSave(configuration);
                    },
                    self.viewmodel.contextId
                );
            } else {
                // No additions or vehicles tab - proceed without cascade
                configuration.cascadeAddPermission = false;
                self.executeSupervisorSave(configuration);
            }
        };

        // Extracted save execution method for supervisor
        this.executeSupervisorSave = function (configuration) {
            console.log("[SUPERVISOR FORM] CALLING UpdateAccessesForPerson NOW with PermissionLevel:", configuration.permissionLevel, "CascadeAddPermission:", configuration.cascadeAddPermission);
            self.viewmodel.setIsBusy(true);
            self.viewmodel.controller.applicationController.getProxyForComponent("VehicleAccessUtilities").UpdateAccessesForPerson(configuration);
        };

        this.initialize = function () {
            /// override visibility of select all and deselect all buttons
            self.viewmodel.Commands.IsSelectAllCommandVisible = ko.pureComputed(function () {
                return self.viewmodel.StatusData.DisplayMode() == 'edit';
            });

            self.viewmodel.Commands.IsDeselectAllCommandVisible = ko.pureComputed(function () {
                return self.viewmodel.StatusData.DisplayMode() == 'edit';
            });

            // Override NewSave button visibility
            self.viewmodel.Commands.IsNewSaveCommandVisible = ko.pureComputed(function () {
                return self.viewmodel.StatusData.DisplayMode() == 'edit';
            });

            // Tab locking during edit mode to prevent cascading issues with pagination
            // Use the proper DisabledTabs mechanism instead of overriding TabChangedMethod
            self.viewmodel.StatusData.DisplayMode.subscribe(function (newMode) {
                if (newMode === 'edit') {
                    // Disable all tabs except the current one during edit mode
                    // CurrentTabIndex is 1-based (1=Sites, 2=Departments, 3=Models, 4=Vehicles)
                    // But DisabledTabs expects 0-based indices (0=Sites, 1=Departments, 2=Models, 3=Vehicles)
                    var currentTab = self.viewmodel.StatusData.CurrentTabIndex();
                    var allTabs = [0, 1, 2, 3]; // 0-based for DisabledTabs
                    var currentTabZeroBased = currentTab - 1; // Convert to 0-based
                    var tabsToDisable = allTabs.filter(function (tab) { return tab !== currentTabZeroBased; });

                    self.viewmodel.StatusData.DisabledTabs(tabsToDisable);
                    GO.log("[TAB LOCK] Tabs disabled during edit mode:", tabsToDisable, "- save required to enable navigation");
                } else {
                    // Enable all tabs when not in edit mode
                    self.viewmodel.StatusData.DisabledTabs([]);
                    GO.log("[TAB LOCK] All tabs enabled");
                }
            });



            // Override NewSave function - simplified to use unified save approach
            self.viewmodel.NewSave = function () {
                // Since we no longer support multi-tab editing, simply use the unified save method
                self.PerformSave();
            };
        };

        this.vehicleAccessUtilitiesProxy = new FleetXQ.Web.Model.Components.VehicleAccessUtilitiesProxy(this.ObjectsDataSet);

        this.onBeforeSave = function () {
            // SMART GUARD: Check if supervisor form should be active
            var canUnlockVehicle = self.viewmodel.CurrentObject().Data.CanUnlockVehicle();
            var normalDriverAccess = self.viewmodel.CurrentObject().Data.NormalDriverAccess();
            var vorActivateDeactivate = self.viewmodel.CurrentObject().Data.VORActivateDeactivate();
            var shouldSupervise = canUnlockVehicle || normalDriverAccess || vorActivateDeactivate;

            console.log("[SUPERVISOR FORM] onBeforeSave called - Should supervise:", shouldSupervise);

            if (!shouldSupervise) {
                console.log("[SUPERVISOR FORM] GUARD: Not supervisor user, skipping");
                return true; // Let other forms handle it
            }

            // Since we no longer support multi-tab editing, use the simplified bulk save method
            // This method includes cascade prompts and proper change handling
            self.PerformSave();
            return false; // Prevent default save behavior
        }



        this.getObjectsForSiteAccess = function (accesses) {
            var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();

            if (accesses.length === 0) {
                var dummyObject = new FleetXQ.Web.Model.DataObjects.PersonToSiteVehicleNormalAccessViewObject();
                dummyObject.Data.IsDirty(true);
                dummyObject.Data.HasAccess(false);
                // FORCE supervisor permission ID for dummy site access
                dummyObject.Data.PermissionId("BED65D1D-F318-4CBA-8750-A5A19300E042");
                dummyObject.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(dummyObject);
                return dummyObject;
            }

            var objectToSave = undefined;
            // CRITICAL: Use supervisor permission ID (Level 1 - Master)
            var supervisorPermissionId = "BED65D1D-F318-4CBA-8750-A5A19300E042";

            for (var i = 0; i < accesses.length; i++) {
                objectToSave = accesses[i].Clone();
                objectToSave.Data.IsDirty(true);
                // FORCE supervisor permission ID for all site accesses
                objectToSave.Data.PermissionId(supervisorPermissionId);
                objectToSave.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(objectToSave);
            }

            return objectToSave;
        };

        this.getObjectsForDepartmentAccess = function (accesses) {
            var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();

            if (accesses.length === 0) {
                var dummyObject = new FleetXQ.Web.Model.DataObjects.PersonToDepartmentVehicleNormalAccessViewObject();
                dummyObject.Data.IsDirty(true);
                dummyObject.Data.HasAccess(false);
                // FORCE supervisor permission ID for dummy department access
                dummyObject.Data.PermissionId("BED65D1D-F318-4CBA-8750-A5A19300E042");
                dummyObject.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(dummyObject);
                return dummyObject;
            }

            var objectToSave = undefined;
            // CRITICAL: Use supervisor permission ID (Level 1 - Master)
            var supervisorPermissionId = "BED65D1D-F318-4CBA-8750-A5A19300E042";

            for (var i = 0; i < accesses.length; i++) {
                objectToSave = accesses[i].Clone();
                objectToSave.Data.IsDirty(true);
                // FORCE supervisor permission ID for all department accesses
                objectToSave.Data.PermissionId(supervisorPermissionId);
                objectToSave.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(objectToSave);
            }

            return objectToSave;
        };

        this.getObjectsForModelAccess = function (accesses) {
            var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();

            if (accesses.length === 0) {
                var dummyObject = new FleetXQ.Web.Model.DataObjects.PersonToModelVehicleNormalAccessViewObject();
                dummyObject.Data.IsDirty(true);
                dummyObject.Data.HasAccess(false);
                // FORCE supervisor permission ID for dummy model access
                dummyObject.Data.PermissionId("BED65D1D-F318-4CBA-8750-A5A19300E042");
                dummyObject.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(dummyObject);
                return dummyObject;
            }

            var objectToSave = undefined;
            // CRITICAL: Use supervisor permission ID (Level 1 - Master)
            var supervisorPermissionId = "BED65D1D-F318-4CBA-8750-A5A19300E042";

            for (var i = 0; i < accesses.length; i++) {
                objectToSave = accesses[i].Clone();
                objectToSave.Data.IsDirty(true);
                // FORCE supervisor permission ID for all model accesses
                objectToSave.Data.PermissionId(supervisorPermissionId);
                objectToSave.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(objectToSave);
            }

            return objectToSave;
        };

        this.getObjectsForVehicleAccess = function (accesses) {
            var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();

            if (accesses.length === 0) {
                var dummyObject = new FleetXQ.Web.Model.DataObjects.PersonToPerVehicleNormalAccessViewObject();
                dummyObject.Data.IsDirty(true);
                dummyObject.Data.HasAccess(false);
                // FORCE supervisor permission ID for dummy vehicle access
                dummyObject.Data.PermissionId("BED65D1D-F318-4CBA-8750-A5A19300E042");
                dummyObject.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(dummyObject);
                return dummyObject;
            }

            var objectToSave = undefined;
            // CRITICAL: Use supervisor permission ID (Level 1 - Master)
            var supervisorPermissionId = "BED65D1D-F318-4CBA-8750-A5A19300E042";

            for (var i = 0; i < accesses.length; i++) {
                objectToSave = accesses[i].Clone();
                objectToSave.Data.IsDirty(true);
                // FORCE supervisor permission ID for all vehicle accesses
                objectToSave.Data.PermissionId(supervisorPermissionId);
                objectToSave.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(objectToSave);
            }

            return objectToSave;
        };









        // Removed onGetAccessesForVehiclesAsyncSuccess - no longer needed

        this.onAccessChangedSuccess = function () {
            self.viewmodel.setIsBusy(false);
            self.viewmodel.EndEdit();
            // Note: Tab navigation is automatically unlocked when EndEdit() changes DisplayMode from 'edit' to 'view'

            // Reset page numbers to 0 for all tabs after save
            self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.pageNumber(0);
            self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.pageNumber(0);
            self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.pageNumber(0);
            self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.pageNumber(0);

            // Reload Sites tab (tab index 1) - use count+paged loader to update pagination
            var siteConfig = {};
            siteConfig.filterPredicate = 'PersonId == @0';
            siteConfig.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            siteConfig.include = 'Site';
            self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.LoadPersonToSiteVehicleNormalAccessViewObjectCollection(siteConfig);
            console.log("[SUPERVISOR SAVE SUCCESS] Reloaded Sites tab with pagination update");

            // Reload Departments tab (tab index 2) - use count+paged loader to update pagination
            var deptConfig = {};
            deptConfig.filterPredicate = 'PersonId == @0';
            deptConfig.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            deptConfig.include = 'Department';
            self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.LoadPersonToDepartmentVehicleNormalAccessViewObjectCollection(deptConfig);
            console.log("[SUPERVISOR SAVE SUCCESS] Reloaded Departments tab with pagination update");

            // Reload Models tab (tab index 3) - use count+paged loader to update pagination
            var modelConfig = {};
            modelConfig.filterPredicate = 'PersonId == @0';
            modelConfig.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            modelConfig.include = 'Model';
            self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.LoadPersonToModelVehicleNormalAccessViewObjectCollection(modelConfig);
            console.log("[SUPERVISOR SAVE SUCCESS] Reloaded Models tab with pagination update");

            // Reload Vehicles tab (tab index 4) - use count+paged loader to update pagination
            var vehicleConfig = {};
            vehicleConfig.filterPredicate = 'PersonId == @0';
            vehicleConfig.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            vehicleConfig.include = 'Vehicle';
            self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.LoadPersonToPerVehicleNormalAccessViewObjectCollection(vehicleConfig);
            console.log("[SUPERVISOR SAVE SUCCESS] Reloaded Vehicles tab with pagination update");
        };

        this.onAccessChangedError = function () {
            self.viewmodel.ShowError("Failed to change vehicle access", "Error");
            self.viewmodel.StatusData.IsBusy(false);
        };

        this.onNewSaveSuccess = function (data) {
            self.viewmodel.setIsBusy(false);
            self.viewmodel.EndEdit();
            // Note: Tab navigation is automatically unlocked when EndEdit() changes DisplayMode from 'edit' to 'view'

            // Reset page numbers to 0 for all tabs after save
            self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.pageNumber(0);
            self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.pageNumber(0);
            self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.pageNumber(0);
            self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.pageNumber(0);

            // Reload Sites tab - use count+paged loader to update pagination
            var siteConfig = {};
            siteConfig.filterPredicate = 'PersonId == @0';
            siteConfig.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            siteConfig.include = 'Site';
            self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.LoadPersonToSiteVehicleNormalAccessViewObjectCollection(siteConfig);
            console.log("[SUPERVISOR NEW SAVE SUCCESS] Reloaded Sites tab with pagination update");

            // Reload Departments tab - use count+paged loader to update pagination
            var deptConfig = {};
            deptConfig.filterPredicate = 'PersonId == @0';
            deptConfig.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            deptConfig.include = 'Department';
            self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.LoadPersonToDepartmentVehicleNormalAccessViewObjectCollection(deptConfig);
            console.log("[SUPERVISOR NEW SAVE SUCCESS] Reloaded Departments tab with pagination update");

            // Reload Models tab - use count+paged loader to update pagination
            var modelConfig = {};
            modelConfig.filterPredicate = 'PersonId == @0';
            modelConfig.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            modelConfig.include = 'Model';
            self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.LoadPersonToModelVehicleNormalAccessViewObjectCollection(modelConfig);
            console.log("[SUPERVISOR NEW SAVE SUCCESS] Reloaded Models tab with pagination update");

            // Reload Vehicles tab - use count+paged loader to update pagination
            var vehicleConfig = {};
            vehicleConfig.filterPredicate = 'PersonId == @0';
            vehicleConfig.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.CurrentObject().Data.Id() + '" }]';
            vehicleConfig.include = 'Vehicle';
            self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.LoadPersonToPerVehicleNormalAccessViewObjectCollection(vehicleConfig);
            console.log("[SUPERVISOR NEW SAVE SUCCESS] Reloaded Vehicles tab with pagination update");
        };

        this.onNewSaveError = function (error) {
            self.viewmodel.setIsBusy(false);
            self.viewmodel.ShowError("Failed to save vehicle access", "Error");
        };


    }
}());