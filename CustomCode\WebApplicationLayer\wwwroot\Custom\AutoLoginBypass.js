(function () {
    'use strict';

    /**
     * Auto-Login Bypass for FleetXQ Development
     * 
     * This script automatically bypasses the login page and authenticates users
     * with Admin/Admin credentials, then redirects to the Dashboard.
     * 
     * IMPORTANT: This is for development purposes only!
     */

    // Configuration
    var AUTO_LOGIN_CONFIG = {
        enabled: true, // Set to false to disable auto-login
        username: 'Admin',
        password: 'Admin',
        dashboardUrl: '/?originalVirtualDir=#!/Dashboard',
        maxRetries: 3,
        retryDelay: 1000 // milliseconds
    };

    // Utility functions
    function log(message, type) {
        type = type || 'info';
        console.log('[AutoLogin] ' + type.toUpperCase() + ': ' + message);
    }

    function isLoginPage() {
        return window.location.pathname.includes('/Membership/Login.html') ||
            window.location.pathname.includes('/Login.html') ||
            document.querySelector("[data-test-id='LoginUser_UserName']") !== null;
    }

    function isAlreadyAuthenticated() {
        // Check if user is already authenticated by looking for user claims or session
        try {
            if (typeof ApplicationController !== 'undefined' &&
                ApplicationController.viewModel &&
                ApplicationController.viewModel.security &&
                ApplicationController.viewModel.security.currentUserClaims) {
                var claims = ApplicationController.viewModel.security.currentUserClaims();
                return claims && claims.UserName;
            }
        } catch (e) {
            // ApplicationController might not be available yet
        }
        return false;
    }

    function waitForElement(selector, timeout) {
        timeout = timeout || 10000;
        return new Promise(function (resolve, reject) {
            var startTime = Date.now();

            function check() {
                var element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error('Element ' + selector + ' not found within timeout'));
                } else {
                    setTimeout(check, 100);
                }
            }

            check();
        });
    }

    function redirectToDashboard() {
        log('Redirecting to Dashboard...');
        window.location.href = AUTO_LOGIN_CONFIG.dashboardUrl;
    }

    // Method 1: Direct API Authentication (Primary approach)
    async function authenticateViaAPI() {
        log('Attempting direct API authentication...');

        try {
            // Get CSRF token first
            var csrfResponse = await fetch('/dataset/api/goservices/csrf-token');
            if (!csrfResponse.ok) {
                throw new Error('Failed to get CSRF token: ' + csrfResponse.status);
            }

            var csrfData = await csrfResponse.json();
            var csrfToken = csrfData.csrfToken;

            if (!csrfToken) {
                throw new Error('No CSRF token received');
            }

            log('CSRF token obtained: ' + csrfToken.substring(0, 10) + '...');

            // Authenticate with credentials
            var formData = new FormData();
            formData.append('username', AUTO_LOGIN_CONFIG.username);
            formData.append('password', AUTO_LOGIN_CONFIG.password);

            var authResponse = await fetch('/dataset/api/gosecurityprovider/authenticate', {
                method: 'POST',
                headers: {
                    'x-csrf-token': csrfToken
                },
                body: formData
            });

            if (!authResponse.ok) {
                throw new Error('Authentication failed: ' + authResponse.status);
            }

            var authData = await authResponse.json();
            log('Authentication response received');

            // Check for authentication tokens
            var tokensObject = authData?.ObjectsDataSet?.GOSecurityTokensObjectsDataSet?.GOSecurityTokensObjects?.['1'];
            var applicationToken = tokensObject?.ApplicationToken;
            var userToken = tokensObject?.AuthenticationToken;

            if (applicationToken && userToken) {
                log('Authentication successful! Tokens received.');

                // Store tokens in session/localStorage if needed
                if (window.sessionStorage) {
                    sessionStorage.setItem('FleetXQ_ApplicationToken', applicationToken);
                    sessionStorage.setItem('FleetXQ_UserToken', userToken);
                    sessionStorage.setItem('FleetXQ_CSRFToken', csrfToken);
                }

                // Set up a delayed redirect to allow session to establish
                setTimeout(redirectToDashboard, 500);
                return true;
            } else {
                throw new Error('No authentication tokens received');
            }

        } catch (error) {
            log('API authentication failed: ' + error.message, 'error');
            return false;
        }
    }

    // Method 2: Form-based Authentication (Fallback approach)
    async function authenticateViaForm() {
        log('Attempting form-based authentication...');

        try {
            // Wait for username field to be available
            var usernameField = await waitForElement("[data-test-id='LoginUser_UserName']", 5000);

            // Fill username
            usernameField.focus();
            usernameField.value = AUTO_LOGIN_CONFIG.username;
            usernameField.dispatchEvent(new Event('input', { bubbles: true }));
            usernameField.dispatchEvent(new Event('change', { bubbles: true }));

            // Click the first login button (username step)
            var firstLoginButton = document.querySelector("[data-test-id='LoginUser_LoginButton']");
            if (firstLoginButton) {
                log('Clicking username submit button...');
                firstLoginButton.click();

                // Wait for password field
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // Wait for password field
            var passwordField = await waitForElement("[data-test-id='LoginUser_Password']", 5000);

            // Fill password
            passwordField.focus();
            passwordField.value = AUTO_LOGIN_CONFIG.password;
            passwordField.dispatchEvent(new Event('input', { bubbles: true }));
            passwordField.dispatchEvent(new Event('change', { bubbles: true }));

            // Click the second login button (password step)
            var loginButtons = document.querySelectorAll("[data-test-id='LoginUser_LoginButton']");
            var secondLoginButton = loginButtons[1] || loginButtons[0];

            if (secondLoginButton) {
                log('Clicking password submit button...');
                secondLoginButton.click();

                // Wait for authentication to complete
                setTimeout(function () {
                    if (!isLoginPage()) {
                        log('Form authentication successful!');
                        redirectToDashboard();
                    }
                }, 2000);

                return true;
            } else {
                throw new Error('Login button not found');
            }

        } catch (error) {
            log('Form authentication failed: ' + error.message, 'error');
            return false;
        }
    }

    // Main auto-login function
    async function performAutoLogin() {
        if (!AUTO_LOGIN_CONFIG.enabled) {
            log('Auto-login is disabled');
            return;
        }

        if (isAlreadyAuthenticated()) {
            log('User already authenticated, redirecting to dashboard...');
            redirectToDashboard();
            return;
        }

        if (!isLoginPage()) {
            log('Not on login page, skipping auto-login');
            return;
        }

        log('Starting auto-login process...');

        // Try API authentication first
        var apiSuccess = await authenticateViaAPI();

        if (!apiSuccess) {
            log('API authentication failed, trying form-based authentication...');
            await authenticateViaForm();
        }
    }

    // Enhanced initialization with retry logic
    function initializeAutoLogin() {
        var retryCount = 0;

        function tryAutoLogin() {
            performAutoLogin().catch(function (error) {
                log('Auto-login attempt failed: ' + error.message, 'error');

                if (retryCount < AUTO_LOGIN_CONFIG.maxRetries) {
                    retryCount++;
                    log('Retrying auto-login in ' + AUTO_LOGIN_CONFIG.retryDelay + 'ms (attempt ' + retryCount + '/' + AUTO_LOGIN_CONFIG.maxRetries + ')');
                    setTimeout(tryAutoLogin, AUTO_LOGIN_CONFIG.retryDelay);
                } else {
                    log('Auto-login failed after ' + AUTO_LOGIN_CONFIG.maxRetries + ' attempts', 'error');
                }
            });
        }

        // Start the auto-login process
        tryAutoLogin();
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeAutoLogin);
    } else {
        // DOM is already ready
        setTimeout(initializeAutoLogin, 100);
    }

    // Also try on window load as a fallback
    window.addEventListener('load', function () {
        setTimeout(function () {
            if (isLoginPage() && !isAlreadyAuthenticated()) {
                log('Fallback initialization triggered');
                initializeAutoLogin();
            }
        }, 500);
    });

    // Expose configuration for debugging
    window.FleetXQ_AutoLogin = {
        config: AUTO_LOGIN_CONFIG,
        forceLogin: performAutoLogin,
        isLoginPage: isLoginPage,
        isAuthenticated: isAlreadyAuthenticated
    };

    log('Auto-Login Bypass script loaded and ready');

})();




