using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using Microsoft.Extensions.DependencyInjection;
using System.IO;
using System.Linq;
using System.Threading;

namespace FleetXQ.ServiceLayer.Middleware
{
    /// <summary>
    /// Middleware that processes subdomain requests and associates them with dealers
    /// </summary>
    public class SubdomainMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<SubdomainMiddleware> _logger;
        private readonly IServiceProvider _services;

        /// <summary>
        /// Initializes a new instance of the subdomain middleware
        /// </summary>
        /// <param name="next">The next middleware in the pipeline</param>
        /// <param name="logger">Logger for diagnostic information</param>
        /// <param name="services">Service provider for scoped service resolution</param>
        public SubdomainMiddleware(
            RequestDelegate next,
            ILogger<SubdomainMiddleware> logger,
            IServiceProvider services)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _services = services ?? throw new ArgumentNullException(nameof(services));
        }

        /// <summary>
        /// Processes the request to extract and validate subdomain information
        /// </summary>
        /// <param name="context">The HTTP context for the current request</param>
        /// <returns>A task representing the completion of middleware processing</returns>
        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                var subdomain = GetSubdomain(context.Request.Host.Host);

                if (!string.IsNullOrEmpty(subdomain))
                {
                    var dealer = await GetDealerAsync(subdomain);
                    if (dealer != null)
                    {
                        context.Items["CurrentDealer"] = dealer;

                        // Enable response buffering for HTML requests only
                        if (IsHtmlRequest(context) || IsLoginPageRequest(context))
                        {
                            context.Response.OnStarting(() =>
                            {
                                context.Response.Headers.Remove("Content-Length");
                                return Task.CompletedTask;
                            });

                            var originalStream = context.Response.Body;
                            using var buffer = new MemoryStream();
                            context.Response.Body = buffer;

                            await _next(context);

                            buffer.Position = 0;
                            var content = await new StreamReader(buffer).ReadToEndAsync();

                            if (!string.IsNullOrEmpty(content) && content.Contains("</head>"))
                            {
                                var injection = CreateStyleAndScriptInjection(dealer);
                                content = content.Replace("</head>", injection);
                            }

                            context.Response.Body = originalStream;
                            await context.Response.WriteAsync(content);
                            return;
                        }
                    }
                }

                // Check one more time before continuing
                if (!context.RequestAborted.IsCancellationRequested)
                {
                    await _next(context);
                }
            }
            catch (OperationCanceledException) when (context.RequestAborted.IsCancellationRequested)
            {
                _logger.LogWarning("Request cancelled in subdomain middleware");
                // Don't rethrow cancellation exceptions
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SubdomainMiddleware");
                await _next(context);
            }
        }

        private async Task<FleetXQ.Data.DataObjects.DealerDataObject> GetDealerAsync(string subdomain)
        {
            using (var scope = _services.CreateScope())
            {
                var dataFacade = scope.ServiceProvider.GetRequiredService<IDataFacade>();
                try
                {
                    var dealers = await dataFacade.DealerDataProvider.GetCollectionAsync(
                        filterPredicate: "SubDomain == @0",
                        filterArguments: new object[] { subdomain },
                        skipSecurity: true
                    );

                    return dealers?.Count > 0 ? dealers[0] : null;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error retrieving dealer for subdomain: {Subdomain}", subdomain);
                    return null;
                }
            }
        }

        private async Task ProcessDealerResponse(HttpContext context, FleetXQ.Data.DataObjects.DealerDataObject dealer, CancellationToken cancellationToken)
        {
            // Store the original body stream
            var originalBody = context.Response.Body;

            try
            {
                // Create a new memory stream to hold the modified response
                using var newBody = new MemoryStream();
                context.Response.Body = newBody;

                // Continue the middleware pipeline
                await _next(context);

                // Check if the request was cancelled during processing
                if (cancellationToken.IsCancellationRequested)
                {
                    _logger.LogWarning("Request cancelled during response processing");
                    return;
                }

                // Only proceed if we have content and the response looks like HTML
                if (newBody.Length > 0 && IsHtmlResponse(context))
                {
                    // Reset the stream position
                    newBody.Seek(0, SeekOrigin.Begin);

                    // Read the response
                    using var reader = new StreamReader(newBody);
                    var content = await reader.ReadToEndAsync();

                    if (cancellationToken.IsCancellationRequested)
                    {
                        _logger.LogWarning("Request cancelled during content reading");
                        return;
                    }

                    // Create the style and script injection
                    var styleAndScript = CreateStyleAndScriptInjection(dealer);

                    // Find the head closing tag and inject our style
                    var headIndex = content.IndexOf("</head>", StringComparison.OrdinalIgnoreCase);
                    _logger.LogWarning("Head tag index: {Index}", headIndex);

                    if (headIndex != -1)
                    {
                        content = content.Replace("</head>", styleAndScript);
                        _logger.LogWarning("Style injected successfully");
                    }
                    else
                    {
                        _logger.LogWarning("Could not find </head> tag in response");
                    }

                    // Restore the original stream and write the modified content
                    context.Response.Body = originalBody;

                    // Check one final time before writing
                    if (!cancellationToken.IsCancellationRequested)
                    {
                        await context.Response.WriteAsync(content, cancellationToken);
                        _logger.LogWarning("Modified content written back to response");
                    }
                }
                else
                {
                    // If not HTML or empty, just copy the original response
                    context.Response.Body = originalBody;
                    newBody.Seek(0, SeekOrigin.Begin);
                    await newBody.CopyToAsync(originalBody, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during response processing");
                // Restore original body stream
                context.Response.Body = originalBody;
                throw;
            }
        }

        private bool IsHtmlRequest(HttpContext context)
        {
            // Check if it's a request that will return HTML
            var path = context.Request.Path.ToString().ToLower();
            return path.EndsWith(".html") ||
                   path.EndsWith("/") ||
                   context.Request.Headers.Accept.ToString().Contains("text/html");
        }

        private bool IsLoginPageRequest(HttpContext context)
        {
            var path = context.Request.Path.ToString().ToLower();
            return path == "/" ||
                   path == "/membership/login" ||
                   path == "/membership" ||
                   path.Contains("/membership/login.html");
        }

        private bool IsHtmlResponse(HttpContext context)
        {
            return context.Response.ContentType?.Contains("text/html") == true ||
                   context.Response.ContentType?.Contains("text/html") == true;
        }

        private string CreateStyleAndScriptInjection(FleetXQ.Data.DataObjects.DealerDataObject dealer)
        {
            return $@"<style>
/* Hide default login logo initially to prevent flash */
object[data=""../Styles/Images/login.svg""] {{
    opacity: 0 !important;
    transition: opacity 0.3s ease-in-out;
}}

/* Show logo after replacement or fallback */
.dealer-logo-loaded {{
    opacity: 1 !important;
}}

:root {{ --fleet-xq-login-bg-color: {dealer.ThemeColor}; }}
.login-page-rectangle-custom {{ display: none; }}
.login-page-yellow-rectangle-custom {{ display: none; }}
.login-footer-svg-custom {{ display: none; }}
.right-circle-positioning-custom {{ display: none; }}
.loginPage .command-button {{ 
    background-color: color-mix(in srgb, {dealer.ThemeColor} 85%, black) !important;
    border-color: color-mix(in srgb, {dealer.ThemeColor} 85%, black) !important;
    transition: all 0.2s ease-in-out !important;
}}
.loginPage .command-button:hover {{ 
    background-color: color-mix(in srgb, {dealer.ThemeColor} 90%, white) !important;
    border-color: color-mix(in srgb, {dealer.ThemeColor} 90%, white) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}}

/* Theme color styles from knockout binding */
.sidebar-wrapper, .sidebar-offcanvas, .help-header-container {{ background-color: {dealer.ThemeColor} !important; }}

.sidebar .nav-link.active, .sidebar .nav-link.active:hover, .sidebar .nav-link.active:focus, 
.sidebar .btn-toggle.active, .sidebar .btn-toggle.active:hover, .sidebar .btn-toggle.active:focus, 
.sidebar .btn-toggle.active a, .sidebar .btn-toggle.active a:hover, .sidebar .btn-toggle.active a:focus {{
    background-color: color-mix(in srgb, {dealer.ThemeColor} 80%, black) !important;
}}

.header-wrapper.fullwidth.d-flex.flex-column.fixed-top header .container {{
    background-color: #A1A1A1;
}}

.dashboard-filter-container-custom div h4 {{ color: {dealer.ThemeColor} !important; }}

.filter-button-container-custom ul > button:first-child {{ background-color: {dealer.ThemeColor} !important; }}
.filter-button-container-custom ul > button:first-child:hover {{ 
    background-color: color-mix(in srgb, {dealer.ThemeColor} 80%, black) !important; 
}}

#DriverLicenseExpiryViewReport h2, #TodaysPreopCheckReport h2, #TodaysImpactViewReport h2,
#LoggedHoursVersusSeatHoursViewReport h2, #ImpactFrequencyPerTimeSlotViewReport h2,
#ImpactFrequencyPerWeekDayViewReport h2, #ImpactFrequencyPerWeekMonthViewReport h2,
#IncompletedChecklistViewReport h2, #ChecklistStatusView h2,
#CurrentVehicleStatusChartViewReport h2, .tabbed-form-container-custom div:nth-child(2) h2 {{
    color: {dealer.ThemeColor} !important;
}}

.command-button, .btn-primary {{
    background-color: {dealer.ThemeColor} !important;
    border-color: {dealer.ThemeColor} !important;
}}

.command-button:hover, .btn-primary:hover {{
    background-color: color-mix(in srgb, {dealer.ThemeColor} 80%, black) !important;
    border-color: color-mix(in srgb, {dealer.ThemeColor} 80%, black) !important;
}}

.filter-container-custom > div > div > div > span, .person-filter-container-custom > div > div > div > span {{
    color: {dealer.ThemeColor} !important;
}}

.d-flex.align-items-center h4.inline-title {{
    color: {dealer.ThemeColor} !important;
}}
.create-new-form-h4-custom {{
    color: {dealer.ThemeColor} !important;
}}
.person-information-header-custom {{
    background: {dealer.ThemeColor} !important;
}}

.bottom-logo-border-custom, .logo-span-custom, .sidebar #login {{
    display: none;
}}
</style>
<script>
(function() {{
    var defaultLoginLogo = '../Styles/Images/login.svg';
    var defaultNavLogo = 'Styles/Images/secondary-logo2.svg';
    var dealerLoginLogo = '{dealer.LoginLogoInternalName}' ? '../files/{dealer.LoginLogoInternalName}' : null;
    var dealerNavLogo = '{dealer.NavbarLogoInternalName}' ? 'files/{dealer.NavbarLogoInternalName}' : null;
    
    console.log('Logo script initialized', {{
        defaultLoginLogo,
        defaultNavLogo,
        dealerLoginLogo,
        dealerNavLogo
    }});
    
    document.addEventListener('DOMContentLoaded', function() {{
        // Handle login logo
        var loginLogo = document.querySelector('object[data=""' + defaultLoginLogo + '""]');
        if (loginLogo) {{
            if (dealerLoginLogo) {{
                console.log('Creating new login logo:', dealerLoginLogo);
                var newLoginLogo = document.createElement('object');
                newLoginLogo.setAttribute('data', dealerLoginLogo);
                newLoginLogo.setAttribute('width', '270');
                newLoginLogo.setAttribute('height', '150');
                newLoginLogo.className = 'dealer-logo-loaded';
                loginLogo.parentNode.replaceChild(newLoginLogo, loginLogo);
                console.log('Login logo replaced successfully');
            }} else {{
                // No dealer logo, show default logo
                loginLogo.className = 'dealer-logo-loaded';
                console.log('No dealer login logo, showing default');
            }}
        }}
    }});
    
    // Fallback: Show logo after 500ms if nothing happened
    setTimeout(function() {{
        var hiddenLogo = document.querySelector('object[data=""' + defaultLoginLogo + '""]');
        if (hiddenLogo && !hiddenLogo.classList.contains('dealer-logo-loaded')) {{
            hiddenLogo.className = 'dealer-logo-loaded';
            console.log('Fallback: Showing hidden login logo');
        }}
    }}, 500);

    window.addEventListener('load', function() {{
        // Handle navbar logo
        console.log('Setting up navbar logo observer');
        var observer = new MutationObserver(function() {{
            console.log('DOM changed, checking for navbar logo');
            var navLogo = document.querySelector('object[data=""' + defaultNavLogo + '""]');
            console.log('Navbar logo found after change:', navLogo ? 'yes' : 'no');
            
            if (navLogo && dealerNavLogo) {{
                console.log('Found navbar logo, replacing with:', dealerNavLogo);
                var newNavLogo = document.createElement('object');
                newNavLogo.setAttribute('data', dealerNavLogo);
                newNavLogo.setAttribute('width', '170');
                newNavLogo.setAttribute('height', '80');
                navLogo.parentNode.replaceChild(newNavLogo, navLogo);
                console.log('Navbar logo replaced successfully');
            }}
        }});

        observer.observe(document.body, {{ 
            childList: true,
            subtree: true 
        }});
    }});
}})();
</script>
<script src=""Custom/AutoLoginBypass.js""></script>
<script>
// Inline Auto-Login Bypass for FleetXQ Development (Fallback)
// This provides auto-login functionality even if the external script file is not available
(function() {{
    'use strict';
    
    // Only enable auto-login in development environment
    var isDevelopment = window.location.hostname === 'localhost' || 
                       window.location.hostname.includes('localhost') ||
                       window.location.port === '53052';
    
    if (!isDevelopment) {{
        console.log('[AutoLogin] Auto-login disabled in production environment');
        return;
    }}
    
    var config = {{
        enabled: true,
        username: 'Admin',
        password: 'Admin',
        dashboardUrl: '/?originalVirtualDir=#!/Dashboard'
    }};
    
    function log(message) {{
        console.log('[AutoLogin-Inline] ' + message);
    }}
    
    function isLoginPage() {{
        return window.location.pathname.includes('/Membership/Login.html') || 
               document.querySelector(""[data-test-id='LoginUser_UserName']"") !== null;
    }}
    
    async function performAutoLogin() {{
        if (!config.enabled || !isLoginPage()) {{
            return;
        }}
        
        log('Starting inline auto-login...');
        
        try {{
            // Method 1: Try API authentication
            var csrfResponse = await fetch('/dataset/api/goservices/csrf-token');
            var csrfData = await csrfResponse.json();
            var csrfToken = csrfData.csrfToken;
            
            if (csrfToken) {{
                var formData = new FormData();
                formData.append('username', config.username);
                formData.append('password', config.password);
                
                var authResponse = await fetch('/dataset/api/gosecurityprovider/authenticate', {{
                    method: 'POST',
                    headers: {{ 'x-csrf-token': csrfToken }},
                    body: formData
                }});
                
                if (authResponse.ok) {{
                    var authData = await authResponse.json();
                    var tokensObject = authData?.ObjectsDataSet?.GOSecurityTokensObjectsDataSet?.GOSecurityTokensObjects?.['1'];
                    
                    if (tokensObject?.ApplicationToken && tokensObject?.AuthenticationToken) {{
                        log('API authentication successful!');
                        setTimeout(function() {{
                            window.location.href = config.dashboardUrl;
                        }}, 500);
                        return;
                    }}
                }}
            }}
        }} catch (e) {{
            log('API authentication failed: ' + e.message);
        }}
        
        // Method 2: Fallback to form-based authentication
        setTimeout(function() {{
            var usernameField = document.querySelector(""[data-test-id='LoginUser_UserName']"");
            if (usernameField) {{
                log('Using form-based authentication...');
                usernameField.value = config.username;
                usernameField.dispatchEvent(new Event('input', {{ bubbles: true }}));
                
                var firstButton = document.querySelector(""[data-test-id='LoginUser_LoginButton']"");
                if (firstButton) {{
                    firstButton.click();
                    
                    setTimeout(function() {{
                        var passwordField = document.querySelector(""[data-test-id='LoginUser_Password']"");
                        if (passwordField) {{
                            passwordField.value = config.password;
                            passwordField.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            
                            var loginButtons = document.querySelectorAll(""[data-test-id='LoginUser_LoginButton']"");
                            var secondButton = loginButtons[1] || loginButtons[0];
                            if (secondButton) {{
                                secondButton.click();
                            }}
                        }}
                    }}, 500);
                }}
            }}
        }}, 1000);
    }}
    
    // Initialize auto-login
    if (document.readyState === 'loading') {{
        document.addEventListener('DOMContentLoaded', performAutoLogin);
    }} else {{
        setTimeout(performAutoLogin, 100);
    }}
    
    log('Inline auto-login script loaded (development environment only)');
}})();
</script>
</head>";
        }

        private string GetSubdomain(string host)
        {
            if (string.IsNullOrEmpty(host))
            {
                _logger.LogWarning("Empty host received");
                return null;
            }

            _logger.LogWarning("Processing host: '{Host}'", host);

            // Remove port if present
            var hostWithoutPort = host.Split(':')[0].ToLowerInvariant();
            var parts = hostWithoutPort.Split('.');

            _logger.LogWarning("Host without port: '{Host}', Parts count: {Count}", hostWithoutPort, parts.Length);

            // Handle localhost development
            if (parts.Length >= 2 && parts[parts.Length - 1] == "localhost")
            {
                if (parts.Length == 2)
                {
                    var subdomain = parts[0];
                    return subdomain == "www" ? null : subdomain;
                }
                // For multi-level localhost like test.subdomain.localhost
                return parts[0];
            }

            // Handle production domains
            // For domains like: subdomain.pilot.fleetxq.ciifm.com
            // We want to extract "subdomain" as the first part
            if (parts.Length >= 3)
            {
                var potentialSubdomain = parts[0];

                // Skip common prefixes
                if (potentialSubdomain == "www")
                {
                    return null;
                }

                // Check if this looks like a subdomain (not a known service prefix)
                var knownPrefixes = new[] { "www" };
                if (!knownPrefixes.Contains(potentialSubdomain))
                {
                    _logger.LogWarning("Extracted subdomain: '{Subdomain}'", potentialSubdomain);
                    return potentialSubdomain;
                }
            }

            _logger.LogWarning("No subdomain found for host: '{Host}'", hostWithoutPort);
            return null;
        }
    }
}