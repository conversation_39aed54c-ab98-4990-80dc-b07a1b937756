﻿(function () {
    //
    FleetXQ.Web.ViewModels.PersonVehicleAccessFormViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        this.IsModifyCommandVisible = function () {
            return (self.viewmodel.StatusData.DisplayMode() == 'view' && !self.viewmodel.StatusData.IsEmpty() && self.viewmodel.DataStore && self.viewmodel.DataStore.CheckAuthorizationForEntityAndMethod('save'))
                && (ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                    ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EDIT_VEHICLE_ACCESS));
        }

        this.IsSaveCommandVisible = ko.pureComputed(function () {
            return false;
        });





        // Helper method to check if any access is being added (HasAccess changed from false to true)
        this.hasAccessBeingAdded = function (accesses) {
            if (!accesses || accesses.length === 0) return false;

            return accesses.some(function (access) {
                // Check if HasAccess is true and the object is dirty (indicating a change)
                return access.Data.HasAccess() && access.Data.IsDirty();
            });
        };

        // Method to perform the actual save operation
        this.PerformSave = function () {
            GO.log("[NORMAL FORM] PROCEEDING with PerformSave - Setting permissionLevel = 3");

            var configuration = {};
            configuration.caller = self.viewmodel;
            configuration.contextId = self.viewmodel.contextId;
            configuration.successHandler = self.onNewSaveSuccess;
            configuration.errorHandler = self.onNewSaveError;
            configuration.personId = self.viewmodel.PersonObject().Data.Id();
            configuration.permissionLevel = 3;

            // Send only current page data (both HasAccess=true and HasAccess=false) from current tab
            // Server will handle this as partial updates, not complete state replacement
            var currentTab = self.viewmodel.StatusData.CurrentTabIndex();


            GO.log("[NORMAL FORM] Sending current page data from tab:", currentTab);

            // Initialize all collections with empty datasets - server will handle empty datasets properly
            configuration.personToSiteAccesses = self.getObjectsForSiteAccess([]);
            configuration.personToDepartmentAccesses = self.getObjectsForDepartmentAccess([]);
            configuration.personToModelAccesses = self.getObjectsForModelAccess([]);
            configuration.personToVehicleAccesses = self.getObjectsForVehicleAccess([]);

            // Only send data from current tab - include BOTH true and false access states
            if (currentTab == 1) { // Sites tab
                var siteAccesses = self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                    .map(function (item) { return item.CurrentObject(); });
                configuration.personToSiteAccesses = self.getObjectsForSiteAccess(siteAccesses);
                GO.log("[NORMAL FORM] Sending Sites tab data (current page only)");
            } else if (currentTab == 2) { // Departments tab
                var departmentAccesses = self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                    .map(function (item) { return item.CurrentObject(); });
                configuration.personToDepartmentAccesses = self.getObjectsForDepartmentAccess(departmentAccesses);
                GO.log("[NORMAL FORM] Sending Departments tab data (current page only)");
            } else if (currentTab == 3) { // Models tab
                var modelAccesses = self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                    .map(function (item) { return item.CurrentObject(); });
                configuration.personToModelAccesses = self.getObjectsForModelAccess(modelAccesses);
                GO.log("[NORMAL FORM] Sending Models tab data (current page only)");
            } else if (currentTab == 4) { // Vehicles tab
                var vehicleAccesses = self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                    .map(function (item) { return item.CurrentObject(); });
                configuration.personToVehicleAccesses = self.getObjectsForVehicleAccess(vehicleAccesses);
                GO.log("[NORMAL FORM] Sending Vehicles tab data (current page only)");
            }





            // Check if access is being added and prompt for cascade confirmation
            var hasAdditions = false;
            var currentTabData = null;

            if (currentTab == 1) { // Sites tab
                currentTabData = self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                    .map(function (item) { return item.CurrentObject(); });
            } else if (currentTab == 2) { // Departments tab
                currentTabData = self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                    .map(function (item) { return item.CurrentObject(); });
            } else if (currentTab == 3) { // Models tab
                currentTabData = self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.viewModelCollection()
                    .map(function (item) { return item.CurrentObject(); });
            }

            if (currentTabData) {
                hasAdditions = self.hasAccessBeingAdded(currentTabData);
            }

            // If access is being added, prompt user for cascade confirmation
            if (hasAdditions && currentTab <= 3) { // Only prompt for Sites, Departments, and Models (not Vehicles)
                var tabName = currentTab == 1 ? "site" : (currentTab == 2 ? "department" : "model");
                var message = "You are adding " + tabName + " access. Do you want to automatically add access to all related child items?";

                self.viewmodel.controller.applicationController.showConfirmPopup(
                    self.viewmodel,
                    message,
                    "Cascade Access Permission",
                    function (cascadeConfirmed) {
                        configuration.cascadeAddPermission = cascadeConfirmed;
                        self.executeSave(configuration);
                    },
                    self.viewmodel.contextId
                );
            } else {
                // No additions or vehicles tab - proceed without cascade
                configuration.cascadeAddPermission = false;
                self.executeSave(configuration);
            }
        };

        // Extracted save execution method
        this.executeSave = function (configuration) {
            GO.log("[NORMAL FORM] CALLING UpdateAccessesForPerson NOW with PermissionLevel:", configuration.permissionLevel, "CascadeAddPermission:", configuration.cascadeAddPermission);
            self.viewmodel.setIsBusy(true);
            self.viewmodel.controller.applicationController.getProxyForComponent("VehicleAccessUtilities").UpdateAccessesForPerson(configuration);
        };

        this.initialize = function () {
            /// override visibility of select all and deselect all buttons
            self.viewmodel.Commands.IsSelectAllCommandVisible = ko.pureComputed(function () {
                return self.viewmodel.StatusData.DisplayMode() == 'edit';
            });

            self.viewmodel.Commands.IsDeselectAllCommandVisible = ko.pureComputed(function () {
                return self.viewmodel.StatusData.DisplayMode() == 'edit';
            });

            // Override NewSave button visibility
            self.viewmodel.Commands.IsNewSaveCommandVisible = ko.pureComputed(function () {
                return self.viewmodel.StatusData.DisplayMode() == 'edit';
            });

            // Tab locking during edit mode to prevent cascading issues with pagination
            // Use the proper DisabledTabs mechanism instead of overriding TabChangedMethod
            self.viewmodel.StatusData.DisplayMode.subscribe(function (newMode) {
                if (newMode === 'edit') {
                    // Disable all tabs except the current one during edit mode
                    // Both CurrentTabIndex and DisabledTabs use 0-based indices (0=Sites, 1=Departments, 2=Models, 3=Vehicles)
                    var currentTab = self.viewmodel.StatusData.CurrentTabIndex();
                    var allTabs = [0, 1, 2, 3]; // 0-based indices
                    var tabsToDisable = allTabs.filter(function (tab) { return tab !== currentTab; });

                    self.viewmodel.StatusData.DisabledTabs(tabsToDisable);
                    GO.log("[TAB LOCK] Tabs disabled during edit mode:", tabsToDisable, "- save required to enable navigation");
                } else {
                    // Enable all tabs when not in edit mode
                    self.viewmodel.StatusData.DisabledTabs([]);
                    GO.log("[TAB LOCK] All tabs enabled");
                }
            });



            // Override NewSave function
            self.viewmodel.NewSave = function () {
                // With tab locking, users can't see other tabs during edit anyway
                // So skip the complex recalculation and just save the current tab
                // The post-save reload will handle getting fresh data for all tabs
                GO.log("[SAVE] Saving current tab data directly, no recalculation needed");
                self.PerformSave();
            };
        };

        // Legacy getObjectsToSave method removed - functionality replaced by getObjectsFor...Access methods

        this.getObjectsForSiteAccess = function (accesses) {
            // HACK: For empty collections, create a dummy object with marker ID that server can recognize and ignore
            // See https://github.com/generative-objects-org/go-meta-lowcode/issues/351
            // TODO: Fix MapDataSetToJSON to handle empty datasets properly instead of requiring dummy objects
            if (accesses.length === 0) {
                var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();
                var dummyObject = new FleetXQ.Web.Model.DataObjects.PersonToSiteVehicleNormalAccessViewObject();
                dummyObject.Data.IsDirty(true); // Must be true so MapDataSetToJSON includes it
                dummyObject.Data.PersonId('00000000-0000-0000-0000-000000000001'); // Special marker ID for dummy objects
                dummyObject.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(dummyObject);
                return dummyObject; // Return first object of dataset (the dummy)
            }

            var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();
            var firstObject = undefined;
            for (var i = 0; i < accesses.length; i++) {
                var objectToSave = accesses[i].Clone();
                objectToSave.Data.IsDirty(true);
                objectToSave.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(objectToSave);

                if (i === 0) {
                    firstObject = objectToSave;
                }
            }

            // Return the first object (which contains reference to the whole dataset)
            return firstObject;
        };

        this.getObjectsForDepartmentAccess = function (accesses) {
            // HACK: For empty collections, create a dummy object with marker ID that server can recognize and ignore
            // TODO: Fix MapDataSetToJSON to handle empty datasets properly instead of requiring dummy objects
            if (accesses.length === 0) {
                var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();
                var dummyObject = new FleetXQ.Web.Model.DataObjects.PersonToDepartmentVehicleNormalAccessViewObject();
                dummyObject.Data.IsDirty(true); // Must be true so MapDataSetToJSON includes it
                dummyObject.Data.PersonId('00000000-0000-0000-0000-000000000001'); // Special marker ID for dummy objects
                dummyObject.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(dummyObject);
                return dummyObject; // Return first object of dataset (the dummy)
            }

            var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();
            var firstObject = undefined;
            for (var i = 0; i < accesses.length; i++) {
                var objectToSave = accesses[i].Clone();
                objectToSave.Data.IsDirty(true);
                objectToSave.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(objectToSave);

                if (i === 0) {
                    firstObject = objectToSave;
                }
            }

            // Return the first object (which contains reference to the whole dataset)
            return firstObject;
        };

        this.getObjectsForModelAccess = function (accesses) {
            // HACK: For empty collections, create a dummy object with marker ID that server can recognize and ignore
            // TODO: Fix MapDataSetToJSON to handle empty datasets properly instead of requiring dummy objects
            if (accesses.length === 0) {
                var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();
                var dummyObject = new FleetXQ.Web.Model.DataObjects.PersonToModelVehicleNormalAccessViewObject();
                dummyObject.Data.IsDirty(true); // Must be true so MapDataSetToJSON includes it
                dummyObject.Data.PersonId('00000000-0000-0000-0000-000000000001'); // Special marker ID for dummy objects
                dummyObject.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(dummyObject);
                return dummyObject; // Return first object of dataset (the dummy)
            }

            var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();
            var firstObject = undefined;
            for (var i = 0; i < accesses.length; i++) {
                var objectToSave = accesses[i].Clone();
                objectToSave.Data.IsDirty(true);
                objectToSave.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(objectToSave);

                if (i === 0) {
                    firstObject = objectToSave;
                }
            }

            // Return the first object (which contains reference to the whole dataset)
            return firstObject;
        };

        this.getObjectsForVehicleAccess = function (accesses) {
            // HACK: For empty collections, create a dummy object with marker ID that server can recognize and ignore
            // TODO: Fix MapDataSetToJSON to handle empty datasets properly instead of requiring dummy objects
            if (accesses.length === 0) {
                var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();
                var dummyObject = new FleetXQ.Web.Model.DataObjects.PersonToPerVehicleNormalAccessViewObject();
                dummyObject.Data.IsDirty(true); // Must be true so MapDataSetToJSON includes it
                dummyObject.Data.PersonId('00000000-0000-0000-0000-000000000001'); // Special marker ID for dummy objects
                dummyObject.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(dummyObject);
                return dummyObject; // Return first object of dataset (the dummy)
            }

            var dataset = new FleetXQ.Web.Model.DataSets.ObjectsDataSet();
            var firstObject = undefined;
            for (var i = 0; i < accesses.length; i++) {
                var objectToSave = accesses[i].Clone();
                objectToSave.Data.IsDirty(true);
                objectToSave.contextIds.push(self.viewmodel.contextId);
                dataset.AddObject(objectToSave);

                if (i === 0) {
                    firstObject = objectToSave;
                }
            }

            // Return the first object (which contains reference to the whole dataset)
            return firstObject;
        };















        this.onBeforeSave = function () {
            // Legacy save method - now handled by PerformSave with unified UpdateAccessesForPerson
            // Return true to allow the new save mechanism to take over
            GO.log("[LEGACY] onBeforeSave called - delegating to new PerformSave mechanism");
            return true;
        }

        // Legacy success/error handlers removed - now handled by onNewSaveSuccess/onNewSaveError

        this.onNewSaveSuccess = function (data) {
            self.viewmodel.setIsBusy(false);
            self.viewmodel.EndEdit();
            // Note: Tab navigation is automatically unlocked when EndEdit() changes DisplayMode from 'edit' to 'view'

            // Reload ALL tabs to reflect any changes from the save operation
            // This ensures all tabs have fresh data after save, including the current tab
            var currentTab = self.viewmodel.StatusData.CurrentTabIndex();
            var configuration = {};
            configuration.filterPredicate = 'PersonId == @0';
            configuration.filterParameters = '[{ "TypeName" : "System.Guid", "Value" : "' + self.viewmodel.PersonObject().Data.Id() + '" }]';

            GO.log("[SAVE SUCCESS] Current tab was:", currentTab, "- reloading ALL tabs");

            // Reset all tabs to page 1 and reload with updated pagination
            self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.pageNumber(0); // 0-based
            self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.pageNumber(0);
            self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.pageNumber(0);
            self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.pageNumber(0);

            // Reload Sites tab (tab index 0) - use count+paged loader to update pagination
            var siteConfig = Object.assign({}, configuration);
            siteConfig.include = 'Site';
            self.viewmodel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.LoadPersonToSiteVehicleNormalAccessViewObjectCollection(siteConfig);
            GO.log("[SAVE SUCCESS] Reloaded Sites tab with pagination update");

            // Reload Departments tab (tab index 1) - use count+paged loader to update pagination
            var deptConfig = Object.assign({}, configuration);
            deptConfig.include = 'Department';
            self.viewmodel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.LoadPersonToDepartmentVehicleNormalAccessViewObjectCollection(deptConfig);
            GO.log("[SAVE SUCCESS] Reloaded Departments tab with pagination update");

            // Reload Models tab (tab index 2) - use count+paged loader to update pagination
            var modelConfig = Object.assign({}, configuration);
            modelConfig.include = 'Model';
            self.viewmodel.PersonToModelVehicleNormalAccessViewItemsListViewModel.LoadPersonToModelVehicleNormalAccessViewObjectCollection(modelConfig);
            GO.log("[SAVE SUCCESS] Reloaded Models tab with pagination update");

            // Reload Vehicles tab (tab index 3) - use count+paged loader to update pagination
            var vehicleConfig = Object.assign({}, configuration);
            vehicleConfig.include = 'Vehicle';
            self.viewmodel.PersonToPerVehicleNormalAccessViewItemsListViewModel.LoadPersonToPerVehicleNormalAccessViewObjectCollection(vehicleConfig);
            GO.log("[SAVE SUCCESS] Reloaded Vehicles tab with pagination update");
        };

        this.onNewSaveError = function (error) {
            self.viewmodel.setIsBusy(false);
            self.viewmodel.ShowError("Failed to save vehicle access", "Error");
        };
    }
}());