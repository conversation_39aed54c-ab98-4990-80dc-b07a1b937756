﻿{
    "DatabaseServerConnectionString": "data source=.\\SQLEXPRESS; integrated security=SSPI; TrustServerCertificate=True; MultipleActiveResultSets=True",
    "DatabaseConnectionStringTemplate": "data source=.\\SQLEXPRESS; initial catalog={0};integrated security=SSPI; MultipleActiveResultSets=True",
    "IoThubConnectionString": "HostName=au-prod-fleetxq.azure-devices.net;SharedAccessKeyName=iothubowner;SharedAccessKey=S6Y63k3w6R/01sNDWJO5r9eeejMSmHt9oAIoTBHZ/m4=",
    "ConnectionStrings": {
        "ServiceBus": "Endpoint=sb://test-namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=test-key-for-unit-tests"
    },
    "ServiceBus": {
        "VehicleSyncQueue": "vehicle-sync-test"
    }
}