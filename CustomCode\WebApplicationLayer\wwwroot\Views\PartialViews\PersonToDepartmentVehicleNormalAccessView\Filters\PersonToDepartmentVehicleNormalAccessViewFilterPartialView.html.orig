﻿<!--
// This is Generated Code
// You should not modify this code as it may be overwritten
// Generated By Generative Objects 
--> 
<!--B<PERSON>IN MasterFilter "Master Filter Layout" Filter " Person to department vehicle normal access view Filter" Internal name : "PersonToDepartmentVehicleNormalAccessViewFilter"-->
<div>
  <div id="{VIEWNAME}-Filter" class="PersonToDepartmentVehicleNormalAccessViewFilter" data-test-id="26af0ff8-ff23-4c58-a50f-e123a89c1e49">
    <form data-bind="submit: {DATABINDROOT}commands.searchCommand">
      <div class="uiSearchContainer">
        <div class="filterFieldSetContent">
          <div class="filterContentContainer">
            <div class="filterGroupContainer">
              <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-2 row-cols-xxl-2">
                <div class="col" data-bind="visible: {DATABINDROOT}statusData.isDepartmentNameVisible">
                  <span class="filterFieldSelection filterTextField input-group input-group-sm">
                    <label class="input-group-text">
                      <span data-bind="i18n: 'entities/PersonToDepartmentVehicleNormalAccessView/filters/PersonToDepartmentVehicleNormalAccessViewFilter:filterFields.DepartmentName.displayName'">$DISPLAYNAME$</span>
                    </label>
                    <input type="text" class="form-control" data-bind="value: {DATABINDROOT}filterData.fields.DepartmentName" data-test-id="c9980c69-6b82-4147-823f-6ede505de515" />
                  </span>
                </div>
                <div class="col" data-bind="visible: {DATABINDROOT}statusData.isHasAccessVisible">
                  <span class="filterFieldSelection filterSelectionField input-group input-group-sm">
                    <label class="input-group-text">
                      <span data-bind="i18n: 'entities/PersonToDepartmentVehicleNormalAccessView/filters/PersonToDepartmentVehicleNormalAccessViewFilter:filterFields.HasAccess.displayName'">$DISPLAYNAME$</span>
                    </label>
                    <select class="form-control" data-bind="value: {DATABINDROOT}filterData.fields.HasAccessValue, optionsText: 'text', options: {DATABINDROOT}HasAccessValues"></select>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="searchCommandsContainer">
            <button class="command-button btn btn-primary btn-sm" data-bind="click: {DATABINDROOT}commands.searchCommand, i18n: 'buttons.search'" data-test-id="searchCommand">%BUTTON_FILTERS_SEARCH%</button>
            <button class="command-button btn btn-third stack btn-sm" data-bind="click: {DATABINDROOT}commands.clearCommand, i18n: 'buttons.clear'" data-test-id="clearCommand">%BUTTON_FILTERS_CLEAR%</button>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
<!--END MasterFilter "Master Filter Layout" Filter " Person to department vehicle normal access view Filter" Internal name : "PersonToDepartmentVehicleNormalAccessViewFilter"-->
 
