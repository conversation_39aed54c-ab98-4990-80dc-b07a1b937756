<!--
// This is Custom Code - Per Normal Access Filter
// Override of the generated filter to use correct binding context
-->
<!--BEGIN MasterFilter "Master Filter Layout" Filter "Person to per vehicle normal access view Filter" Internal name : "PersonToPerVehicleNormalAccessViewFilter"-->
<div>
    <div id="{VIEWNAME}-Filter" class="PersonToPerVehicleNormalAccessViewFilter"
        data-test-id="1730190a-3719-4d87-bbf3-a2595141c89c">
        <form
            data-bind="submit: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.commands.searchCommand">
            <div class="uiSearchContainer">
                <div class="filterFieldSetContent">
                    <div class="filterContentContainer">
                        <div class="filterGroupContainer">
                            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-2 row-cols-xxl-2">
                                <div class="col"
                                    data-bind="visible: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.statusData.isHireNoVisible">
                                    <span class="filterFieldSelection filterTextField input-group input-group-sm">
                                        <label class="input-group-text">
                                            <span
                                                data-bind="i18n: 'entities/PersonToPerVehicleNormalAccessView/filters/PersonToPerVehicleNormalAccessViewFilter:filterFields.HireNo.displayName'">$DISPLAYNAME$</span>
                                        </label>
                                        <input type="text" class="form-control"
                                            data-bind="value: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.filterData.fields.HireNo"
                                            data-test-id="4ee436ee-ffb4-4ec2-b238-65127d45f271" />
                                    </span>
                                </div>
                                <div class="col"
                                    data-bind="visible: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.statusData.isHasAccessVisible">
                                    <span class="filterFieldSelection filterSelectionField input-group input-group-sm">
                                        <label class="input-group-text">
                                            <span
                                                data-bind="i18n: 'entities/PersonToPerVehicleNormalAccessView/filters/PersonToPerVehicleNormalAccessViewFilter:filterFields.HasAccess.displayName'">$DISPLAYNAME$</span>
                                        </label>
                                        <select class="form-control"
                                            data-bind="value: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.filterData.fields.HasAccessValue, optionsText: 'text', options: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.HasAccessValues"></select>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="searchCommandsContainer">
                        <button class="command-button btn btn-primary btn-sm"
                            data-bind="click: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.commands.searchCommand, i18n: 'buttons.search'"
                            data-test-id="searchCommand">%BUTTON_FILTERS_SEARCH%</button>
                        <button class="command-button btn btn-third stack btn-sm"
                            data-bind="click: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.commands.clearCommand, i18n: 'buttons.clear'"
                            data-test-id="clearCommand">%BUTTON_FILTERS_CLEAR%</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<!--END MasterFilter "Master Filter Layout" Filter "Person to per vehicle normal access view Filter" Internal name : "PersonToPerVehicleNormalAccessViewFilter"-->