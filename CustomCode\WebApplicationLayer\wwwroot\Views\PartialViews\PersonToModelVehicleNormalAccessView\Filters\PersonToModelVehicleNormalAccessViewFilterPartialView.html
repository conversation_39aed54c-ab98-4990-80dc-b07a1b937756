<!--
// This is Custom Code
// Override of the generated filter to use correct binding context
-->
<!--BEGIN MasterFilter "Master Filter Layout" Filter " Person to model vehicle normal access view Filter" Internal name : "PersonToModelVehicleNormalAccessViewFilter"-->
<div>
    <div id="{VIEWNAME}-Filter" class="PersonToModelVehicleNormalAccessViewFilter"
        data-test-id="69d7ddaf-e4f8-43fe-8831-34524d917db1">
        <form
            data-bind="submit: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.commands.searchCommand">
            <div class="uiSearchContainer">
                <div class="filterFieldSetContent">
                    <div class="filterContentContainer">
                        <div class="filterGroupContainer">
                            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-2 row-cols-xxl-2">
                                <div class="col"
                                    data-bind="visible: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.statusData.isModelNameVisible">
                                    <span class="filterFieldSelection filterTextField input-group input-group-sm">
                                        <label class="input-group-text">
                                            <span
                                                data-bind="i18n: 'entities/PersonToModelVehicleNormalAccessView/filters/PersonToModelVehicleNormalAccessViewFilter:filterFields.ModelName.displayName'">$DISPLAYNAME$</span>
                                        </label>
                                        <input type="text" class="form-control"
                                            data-bind="value: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.filterData.fields.ModelName"
                                            data-test-id="95e88b83-e73f-43e2-b7f7-36b2e0e9fbba" />
                                    </span>
                                </div>
                                <div class="col"
                                    data-bind="visible: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.statusData.isHasAccessVisible">
                                    <span class="filterFieldSelection filterSelectionField input-group input-group-sm">
                                        <label class="input-group-text">
                                            <span
                                                data-bind="i18n: 'entities/PersonToModelVehicleNormalAccessView/filters/PersonToModelVehicleNormalAccessViewFilter:filterFields.HasAccess.displayName'">$DISPLAYNAME$</span>
                                        </label>
                                        <select class="form-control"
                                            data-bind="value: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.filterData.fields.HasAccessValue, optionsText: 'text', options: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.HasAccessValues"></select>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="searchCommandsContainer">
                        <button class="command-button btn btn-primary btn-sm"
                            data-bind="click: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.commands.searchCommand, i18n: 'buttons.search'"
                            data-test-id="searchCommand">%BUTTON_FILTERS_SEARCH%</button>
                        <button class="command-button btn btn-third stack btn-sm"
                            data-bind="click: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.commands.clearCommand, i18n: 'buttons.clear'"
                            data-test-id="clearCommand">%BUTTON_FILTERS_CLEAR%</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<!--END MasterFilter "Master Filter Layout" Filter " Person to model vehicle normal access view Filter" Internal name : "PersonToModelVehicleNormalAccessViewFilter"-->