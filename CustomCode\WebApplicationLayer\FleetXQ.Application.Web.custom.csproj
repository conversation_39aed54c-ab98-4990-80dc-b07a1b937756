﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <OutputType>Library</OutputType>
    <RunPostBuildEvent>Always</RunPostBuildEvent>
    <DisableFastUpToDateCheck>true</DisableFastUpToDateCheck>
  </PropertyGroup>
  <Target Name="Merge_EntityFactories" AfterTargets="CopyFilesToOutputDirectory">
    <ItemGroup>
      <JsFilesEntityFactories Include="wwwroot/Model/readme.js" />
      <JsFilesEntityFactories Include="wwwroot/Model/DataObjects/*.custom.js" />
      <EntityFactoriesFileLines Include="$([System.IO.File]::ReadAllText(%(JsFilesEntityFactories.Identity)))" />
    </ItemGroup>
    <WriteLinesToFile File="wwwroot/Custom/EntityFactories.js" Lines="@(EntityFactoriesFileLines)" Overwrite="true" />
  </Target>
  <Target Name="Merge_EntityValidators" AfterTargets="CopyFilesToOutputDirectory">
    <ItemGroup>
      <JsFilesEntityValidators Include="wwwroot/Model/readme.js" />
      <JsFilesEntityValidators Include="wwwroot/Model/DataObjectValidators/*.custom.js" />
      <EntityValidatorsFileLines Include="$([System.IO.File]::ReadAllText(%(JsFilesEntityValidators.Identity)))" />
    </ItemGroup>
    <WriteLinesToFile File="wwwroot/Custom/EntityValidators.js" Lines="@(EntityValidatorsFileLines)" Overwrite="true" />
  </Target>
  <Target Name="Merge_CustomFields" AfterTargets="CopyFilesToOutputDirectory">
    <ItemGroup>
      <JsFilesCustomFields Include="wwwroot/Model/readme.js" />
      <JsFilesCustomFields Include="wwwroot/Model/CustomFields/*.custom.js" />
      <CustomFieldsFileLines Include="$([System.IO.File]::ReadAllText(%(JsFilesCustomFields.Identity)))" />
    </ItemGroup>
    <WriteLinesToFile File="wwwroot/Custom/CustomFields.js" Lines="@(CustomFieldsFileLines)" Overwrite="true" />
  </Target>
  <Target Name="Merge_ViewModels" AfterTargets="CopyFilesToOutputDirectory">
    <ItemGroup>
      <JsFilesViewModels Include="wwwroot/ViewModels/readme.js" />
      <JsFilesViewModels Include="wwwroot/ViewModels/*.custom.js" />
      <JsFilesViewModels Include="wwwroot/ViewModels/*/*.custom.js" />
      <JsFilesViewModels Include="wwwroot/ViewModels/*/Filters/*.custom.js" />
      <ViewModelsFileLines Include="$([System.IO.File]::ReadAllText(%(JsFilesViewModels.Identity)))" />
    </ItemGroup>
    <WriteLinesToFile File="wwwroot/Custom/ViewModels.js" Lines="@(ViewModelsFileLines)" Overwrite="true" />
  </Target>
  <Target Name="Merge_Controllers" AfterTargets="CopyFilesToOutputDirectory">
    <ItemGroup>
      <JsFilesControllers Include="wwwroot/Controllers/readme.js" />
      <JsFilesControllers Include="wwwroot/Controllers/*.custom.js" />
      <ControllersFileLines Include="$([System.IO.File]::ReadAllText(%(JsFilesControllers.Identity)))" />
    </ItemGroup>
    <WriteLinesToFile File="wwwroot/Custom/PageControllers.js" Lines="@(ControllersFileLines)" Overwrite="true" />
  </Target>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(ProjectDir)wwwroot\Views\*.*&quot; &quot;..\..\GeneratedCode\WebApplicationLayer\wwwroot\Views&quot; /Y /I /S" />
    <Exec Command="xcopy &quot;$(ProjectDir)wwwroot\Custom\*.*&quot; &quot;..\..\GeneratedCode\WebApplicationLayer\wwwroot\Custom&quot; /Y /I" />
    <Exec Command="xcopy &quot;$(ProjectDir)wwwroot\Extra\*.*&quot; &quot;..\..\GeneratedCode\WebApplicationLayer\wwwroot&quot; /Y /I /S" />
    <Exec Command="xcopy &quot;$(ProjectDir)wwwroot\Model\Components\*.*&quot; &quot;..\..\GeneratedCode\WebApplicationLayer\wwwroot\Model\Components&quot; /Y /I" />
    <Exec Command="xcopy &quot;$(ProjectDir)wwwroot\Application\*.*&quot; &quot;..\..\GeneratedCode\WebApplicationLayer\wwwroot\Custom\Application&quot; /Y /I /S" />
    <Exec Command="xcopy &quot;$(ProjectDir)wwwroot\Model\Components\*.*&quot; &quot;..\..\GeneratedCode\WebApplicationLayer\wwwroot\Model\Components&quot; /Y /I /S" />
  </Target>
  <ItemGroup>
    <None Remove="wwwroot\ViewModels\AccessGroupTemplate\ApplyAccessGroupTemplateFormViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\AlertSubscription\AlertSubscriptionFormViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\AllImpactsView\ImpactLocationFormViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\Card\SelectOnDemandUsersGridViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\Customer\AddCustomerCategoryFormViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\ImportJobStatus\UploadFileFormViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\Model\ApplyCategoryToCustomersFormViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\Model\SelectDealerCategoryGridViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\PerVehicleNormalCardAccess\PerVehicleNormalCardAccessFormViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\VehicleAlertSubscription\VehicleAlertItemsGridViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\VehicleAlertSubscription\VehicleAlertSubscriptionGridViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\Vehicle\Filters\SelectVehicleForAlertFilterViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\Vehicle\VehicleOnDemandSettingsFormViewModel.custom.js" />
    <None Remove="wwwroot\ViewModels\Vehicle\VehiclesGPSLocationsGridViewModel.custom.js" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="wwwroot\ViewModels\AccessGroupTemplate\ApplyAccessGroupTemplateFormViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\AlertSubscription\AlertSubscriptionFormViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\AllImpactsView\ImpactLocationFormViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\BroadcastMessage\BroadcastMessageFormViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\Card\SelectOnDemandUsersGridViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\Customer\AddCustomerCategoryFormViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\ImportJobStatus\UploadFileFormViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\Model\ApplyCategoryToCustomersFormViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\Model\SelectDealerCategoryGridViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\PerVehicleNormalCardAccess\PerVehicleNormalCardAccessFormViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\VehicleAlertSubscription\VehicleAlertItemsGridViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\VehicleAlertSubscription\VehicleAlertSubscriptionGridViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\VehicleBroadcastMessage\VehicleBroadcastMessageGridViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\Vehicle\Filters\SelectVehicleForAlertFilterViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\Vehicle\VehicleOnDemandSettingsFormViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="wwwroot\ViewModels\Vehicle\VehiclesGPSLocationsGridViewModel.custom.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="wwwroot\ViewModels\DepartmentHourSettings\" />
    <Folder Include="wwwroot\Views\PartialViews\PersonToDepartmentVehicleNormalAccessView\Filters\" />
    <Folder Include="wwwroot\Views\PartialViews\PersonToSiteVehicleNormalAccessView\" />
  </ItemGroup>
</Project>