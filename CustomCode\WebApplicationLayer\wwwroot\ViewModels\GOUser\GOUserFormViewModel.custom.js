﻿(function () {
    FleetXQ.Web.ViewModels.GOUserFormViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        // create website access
        this.IsCreateNewCommandVisible = function () {
            return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_CREATE_WEBSITE_ACCESS);
        };

        // Filter Website Access Level options based on current user's access level
        this.filterWebsiteAccessLevelOptions = function () {
            try {
                var currentUserClaims = ApplicationController.viewModel.security.currentUserClaims();

                // Check if user is administrator - if so, show all options
                if (currentUserClaims && currentUserClaims.role === 'Administrator') {
                    console.log('GOUserFormViewModel: User is Administrator, showing all options');
                    return; // Don't filter, show all options
                }

                var currentUserWAL = currentUserClaims.WAL;

                // If WAL is null, only show Department (level 0) - lowest level in hierarchy
                if (currentUserWAL === null || currentUserWAL === undefined) {
                    console.log('GOUserFormViewModel: WAL is null, showing only Department options (lowest level)');
                    var departmentOnlyValues = self.viewmodel.GOUserObject().Data.WebsiteAccessLevelValues().filter(function (item) {
                        return item.selectvalue === 0; // Only Department level (lowest)
                    });
                    self.viewmodel.GOUserObject().Data.WebsiteAccessLevelValues(departmentOnlyValues);
                    return;
                }

                // Convert to number if it's a string
                var userAccessLevel = parseInt(currentUserWAL);

                if (isNaN(userAccessLevel)) {
                    console.warn('GOUserFormViewModel: Invalid WAL value:', currentUserWAL);
                    return;
                }

                // Define the hierarchy levels for better logging
                var hierarchyLevels = {
                    2: 'Customer',           // Highest level
                    1: 'Site',               // High level
                    4: 'Multi Department',   // Medium level
                    0: 'Department'          // Lowest level
                };

                // Define access level hierarchy (what each level can see)
                var accessLevelHierarchy = {
                    2: [2, 1, 4, 0],        // Customer can see all levels
                    1: [1, 4, 0],           // Site can see Site, Multi Department, Department
                    4: [4, 0],              // Multi Department can see Multi Department, Department
                    0: [0]                  // Department can only see Department
                };

                console.log('GOUserFormViewModel: Filtering options for user WAL:', userAccessLevel, '(', hierarchyLevels[userAccessLevel], ')');

                // Filter the WebsiteAccessLevelValues based on user's access level hierarchy
                var allowedLevels = accessLevelHierarchy[userAccessLevel] || [];
                var filteredValues = self.viewmodel.GOUserObject().Data.WebsiteAccessLevelValues().filter(function (item) {
                    return allowedLevels.indexOf(item.selectvalue) !== -1;
                });

                console.log('GOUserFormViewModel: Filtered options:', filteredValues.map(function (item) {
                    return FleetXQ.Web.Model.DataObjects.WebsiteAccessLevelEnum[item.selectvalue];
                }));

                // Update the observable array with filtered values
                self.viewmodel.GOUserObject().Data.WebsiteAccessLevelValues(filteredValues);
            } catch (error) {
                console.error('GOUserFormViewModel: Error filtering Website Access Level options:', error);
            }
        };

        // Custom validation to prevent setting Website Access Level higher than user's own level
        this.validateWebsiteAccessLevel = function () {
            try {
                var currentUserClaims = ApplicationController.viewModel.security.currentUserClaims();
                var selectedWAL = self.viewmodel.GOUserObject().Data.WebsiteAccessLevel();

                // Check if user is administrator - if so, allow any selection
                if (currentUserClaims && currentUserClaims.role === 'Administrator') {
                    console.log('GOUserFormViewModel: User is Administrator, allowing any Website Access Level selection');
                    return; // Don't validate, allow any selection
                }

                if (selectedWAL !== null && selectedWAL !== undefined) {
                    var selectedAccessLevel = parseInt(selectedWAL);

                    if (isNaN(selectedAccessLevel)) {
                        return;
                    }

                    var currentUserWAL = currentUserClaims.WAL;

                    // If WAL is null, only allow Department (level 0) - lowest level in hierarchy
                    if (currentUserWAL === null) {
                        if (selectedAccessLevel < 0) {
                            // Reset to Department level (lowest)
                            self.viewmodel.GOUserObject().Data.WebsiteAccessLevel(0);

                            console.log('GOUserFormViewModel: Access level restricted from', selectedAccessLevel, '(', FleetXQ.Web.Model.DataObjects.WebsiteAccessLevelEnum[selectedAccessLevel], ') to Department (0) - lowest level');

                            // Show warning message
                            if (self.viewmodel.controller && self.viewmodel.controller.applicationController) {
                                self.viewmodel.controller.applicationController.showAlertPopup(
                                    self.viewmodel,
                                    "You cannot set a Website Access Level lower than Department. The value has been adjusted to Department.",
                                    "Access Level Restriction"
                                );
                            }
                        }
                        return;
                    }

                    // If WAL is undefined, allow any selection
                    if (currentUserWAL === undefined) {
                        return;
                    }

                    var userAccessLevel = parseInt(currentUserWAL);

                    if (isNaN(userAccessLevel)) {
                        return;
                    }

                    // Check if the selected level is allowed for the user's access level
                    var accessLevelHierarchy = {
                        2: [2, 1, 4, 0],        // Customer can see all levels
                        1: [1, 4, 0],           // Site can see Site, Multi Department, Department
                        4: [4, 0],              // Multi Department can see Multi Department, Department
                        0: [0]                  // Department can only see Department
                    };

                    var allowedLevels = accessLevelHierarchy[userAccessLevel] || [];

                    if (allowedLevels.indexOf(selectedAccessLevel) === -1) {
                        // Reset to user's level
                        self.viewmodel.GOUserObject().Data.WebsiteAccessLevel(userAccessLevel);

                        // Define hierarchy levels for better logging
                        var hierarchyLevels = {
                            2: 'Customer',           // Highest level
                            1: 'Site',               // High level
                            4: 'Multi Department',   // Medium level
                            0: 'Department'          // Lowest level
                        };

                        console.log('GOUserFormViewModel: Access level restricted from', selectedAccessLevel, '(', FleetXQ.Web.Model.DataObjects.WebsiteAccessLevelEnum[selectedAccessLevel], ') to', userAccessLevel, '(', hierarchyLevels[userAccessLevel], ')');

                        // Show warning message
                        if (self.viewmodel.controller && self.viewmodel.controller.applicationController) {
                            self.viewmodel.controller.applicationController.showAlertPopup(
                                self.viewmodel,
                                "You cannot set a Website Access Level that is not allowed for your access level. The value has been adjusted to " + FleetXQ.Web.Model.DataObjects.WebsiteAccessLevelEnum[userAccessLevel] + ".",
                                "Access Level Restriction"
                            );
                        }
                    }
                }
            } catch (error) {
                console.error('GOUserFormViewModel: Error validating Website Access Level:', error);
            }
        };

        // Custom WebsiteAccessLevelOptionsAfterRender to ensure filtering is applied
        this.WebsiteAccessLevelOptionsAfterRenderCustom = function (option, item) {
            try {
                var currentUserClaims = ApplicationController.viewModel.security.currentUserClaims();

                // Check if user is administrator - if so, show all options
                if (currentUserClaims && currentUserClaims.role === 'Administrator') {
                    item.visible(true);
                    ko.applyBindingsToNode(option, { visible: item.visible }, item);
                    return;
                }

                var currentUserWAL = currentUserClaims.WAL;

                // If WAL is null, only show Department (level 0) - lowest level in hierarchy
                if (currentUserWAL === null) {
                    if (item.selectvalue === 0) {
                        item.visible(true);
                    } else {
                        item.visible(false);
                    }
                }
                // If WAL is undefined, show all options
                else if (currentUserWAL === undefined) {
                    item.visible(true);
                }
                // If WAL has a value, filter based on user's level following the hierarchy
                else if (currentUserWAL !== null && currentUserWAL !== undefined) {
                    var userAccessLevel = parseInt(currentUserWAL);

                    if (!isNaN(userAccessLevel)) {
                        // Define the hierarchy levels (from highest to lowest access)
                        var hierarchyLevels = {
                            2: 'Customer',           // Highest level
                            1: 'Site',               // High level
                            4: 'Multi Department',   // Medium level
                            0: 'Department'          // Lowest level
                        };

                        // Define access level hierarchy (what each level can see)
                        var accessLevelHierarchy = {
                            2: [2, 1, 4, 0],        // Customer can see all levels
                            1: [1, 4, 0],           // Site can see Site, Multi Department, Department
                            4: [4, 0],              // Multi Department can see Multi Department, Department
                            0: [0]                  // Department can only see Department
                        };

                        // Check if the item's level is within the user's allowed range
                        var allowedLevels = accessLevelHierarchy[userAccessLevel] || [];
                        var isVisible = allowedLevels.indexOf(item.selectvalue) !== -1;

                        if (isVisible) {
                            item.visible(true);
                        } else {
                            item.visible(false);
                        }
                    }
                }

                ko.applyBindingsToNode(option, { visible: item.visible }, item);
            } catch (error) {
                console.error('GOUserFormViewModel: Error in WebsiteAccessLevelOptionsAfterRender:', error);
                // Fallback to default behavior
                ko.applyBindingsToNode(option, { visible: item.visible }, item);
            }
        };

        this.initialize = function () {
            self.viewmodel.StatusData.IsFullNameReadOnly = function () {
                return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                    !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EDIT_WEBSITE_ACCESS);
            }

            self.viewmodel.StatusData.IsUserNameReadOnly = function () {
                return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                    !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EDIT_WEBSITE_ACCESS);
            }

            self.viewmodel.StatusData.IsEmailAddressReadOnly = function () {
                return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                    !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EDIT_WEBSITE_ACCESS);
            }

            self.viewmodel.StatusData.IsPasswordReadOnly = function () {
                return ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                    !ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EDIT_WEBSITE_ACCESS);
            }

            // Filter Website Access Level options based on current user's access level
            self.filterWebsiteAccessLevelOptions();

            // Subscribe to GOUserObject changes to reapply filtering when object is loaded
            self.viewmodel.GOUserObject.subscribe(function (newValue) {
                if (newValue && newValue.Data && newValue.Data.WebsiteAccessLevelValues) {
                    // Small delay to ensure the object is fully loaded
                    setTimeout(function () {
                        self.filterWebsiteAccessLevelOptions();
                    }, 100);
                }
            });

            // Subscribe to GOUserLoaded event to ensure filtering is applied after loading
            self.viewmodel.Events.GOUserLoaded.subscribe(function () {
                setTimeout(function () {
                    self.filterWebsiteAccessLevelOptions();
                }, 100);
            });

            // Subscribe to WebsiteAccessLevel changes to validate the selection
            self.viewmodel.GOUserObject().Data.WebsiteAccessLevel.subscribe(function (newValue) {
                if (newValue !== null && newValue !== undefined) {
                    self.validateWebsiteAccessLevel();
                }
            });

            // Set the custom WebsiteAccessLevelOptionsAfterRender function
            if (self.viewmodel.GOUserObject().WebsiteAccessLevelOptionsAfterRenderCustom === undefined) {
                self.viewmodel.GOUserObject().WebsiteAccessLevelOptionsAfterRenderCustom = self.WebsiteAccessLevelOptionsAfterRenderCustom;
            }

            console.log('GOUserFormViewModel: Custom view model initialized with Website Access Level filtering');
        }

    }
}());
