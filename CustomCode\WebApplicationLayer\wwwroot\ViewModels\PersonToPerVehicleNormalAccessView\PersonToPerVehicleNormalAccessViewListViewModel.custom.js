////////////////////////////////////////////////////////////////////////////////////////////
// Custom Extension for PersonToPerVehicleNormalAccessViewListViewModel
// Adds pagination functionality to the generated list view model
////////////////////////////////////////////////////////////////////////////////////////////

(function () {
    FleetXQ.Web.ViewModels.PersonToPerVehicleNormalAccessViewListViewModelCustom = function (viewModel) {
        var self = this;
        this.viewModel = viewModel;

        // Pagination properties
        this.viewModel.totalPageNumber = ko.observable(0);
        this.viewModel.totalCollection = ko.observable(0);
        this.viewModel.ignorePageChange = false;
        this.viewModel.pageNumber = ko.observable(0); // 0 based
        this.viewModel.pageSize = 15;

        // Pagination UI properties and methods
        this.viewModel.paginationSettings = {
            iNbPageNumberToShow: 5, // always odd
            methods: {
                updatePagination: function () {
                    // This is called everytime pageNumber's value or totalPageNumber's value has changed, recalculating the pagenumbers to display
                    var iStart = 0,
                        iEnd = self.viewModel.totalPageNumber(),
                        pageCollection = [];
                    if (self.viewModel.paginationSettings.iNbPageNumberToShow !== null) {
                        var iDistribution = Math.floor(self.viewModel.paginationSettings.iNbPageNumberToShow / 2); // from 5 to 2, 3 to 1, 1 to 0 ...
                        iStart = Math.max(iStart, self.viewModel.pageNumber() - iDistribution); // 3 - 2
                        iEnd = Math.min(iEnd, iStart + self.viewModel.paginationSettings.iNbPageNumberToShow); // 3 + 2

                        // re-adjust the start if we reached the end
                        iStart = Math.max(0, iEnd - self.viewModel.paginationSettings.iNbPageNumberToShow);
                    }

                    // build the page numbers
                    for (var i = iStart; i < iEnd; i++) {
                        pageCollection.push({
                            pagenumber: i,
                            domClass: self.viewModel.pageNumber() == i ? true : false,
                            text: i + 1
                        });
                    }

                    // return the correct pageNumbers to the pageItems computed
                    return pageCollection;
                },
                paginationClickHandler: function (data, e) {
                    e.preventDefault();
                    e.stopPropagation();
                    // should fire the event
                    var targetPage = parseInt(e.currentTarget.getAttribute('data-pagenumber'));
                    if (targetPage > -1 && targetPage < self.viewModel.totalPageNumber()) {
                        // Check if we're in edit mode and have changes
                        if (self.viewModel.StatusData && self.viewModel.StatusData.DisplayMode() === 'edit' && self.hasPageChanges()) {
                            self.pendingPageNumber = targetPage;
                            self.showNavigationConfirmation();
                        } else {
                            self.viewModel.pageNumber(targetPage);
                        }
                    }
                }
            }
        };

        this.viewModel.pageItems = ko.computed(function () {
            return self.viewModel.paginationSettings.methods.updatePagination();
        });

        // Store reference to original load method (will be set in initialize)
        this.originalLoadMethod = null;

        // Track original state for change detection
        this.originalPageState = {};
        this.pendingPageNumber = null;

        // Filter integration (following PersonGridViewModel pattern)
        // Use existing filter from generated viewmodel as base filter
        this.viewModel.baseFilterPredicate = this.viewModel.filterPredicate;
        this.viewModel.baseFilterParameters = this.viewModel.filterParameters;
        this.viewModel.baseFilterParametersCount = 0;

        // Create filter viewmodel (like PersonGrid does)
        this.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel = new FleetXQ.Web.ViewModels.Filters.PersonToPerVehicleNormalAccessViewFilterViewModel(
            self.viewModel.controller, null, null, self.viewModel.contextId
        );

        // Ensure filter viewmodel is properly initialized
        if (this.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel.initialize) {
            this.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel.initialize();
        }

        // Filter integration methods
        this.addFilterPredicateAndParameters = function (predicate, parameters) {
            self.viewModel.filterPredicate = self.viewModel.baseFilterPredicate;
            self.viewModel.filterParameters = self.viewModel.baseFilterParameters;

            if (self.viewModel.filterPredicate !== '' && self.viewModel.filterPredicate !== null) {
                self.viewModel.filterPredicate += ' && ';
            } else {
                self.viewModel.filterPredicate = '';
            }
            self.viewModel.filterPredicate += '(' + predicate + ')';

            if (self.viewModel.baseFilterParameters != null && self.viewModel.baseFilterParameters != '') {
                if (parameters != null && parameters != '') {
                    self.viewModel.filterParameters = JSON.parse(self.viewModel.baseFilterParameters).concat(parameters);
                    self.viewModel.filterParameters = JSON.stringify(self.viewModel.filterParameters);
                } else {
                    self.viewModel.filterParameters = self.viewModel.baseFilterParameters;
                }
            } else {
                if (parameters != null && parameters != '') {
                    self.viewModel.filterParameters = JSON.stringify(parameters);
                } else {
                    self.viewModel.filterParameters = null;
                }
            }

            self.viewModel.pageNumber(0);
            self.viewModel.LoadPersonToPerVehicleNormalAccessViewObjectCollection();
        };

        this.clearFilterPredicate = function () {
            self.viewModel.filterPredicate = self.viewModel.baseFilterPredicate;
            self.viewModel.filterParameters = self.viewModel.baseFilterParameters;

            var baseFilterParametersArray = self.viewModel.baseFilterParameters == null || self.viewModel.baseFilterParameters == '' ? [] : JSON.parse(self.viewModel.baseFilterParameters);
            self.viewModel.pageNumber(0);
            var predicateAndParameters = self.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel.getFilterPredicateAndParameters(baseFilterParametersArray.length);
            if (predicateAndParameters.filterPredicate !== null && predicateAndParameters.filterPredicate !== "") {
                self.addFilterPredicateAndParameters(predicateAndParameters.filterPredicate, predicateAndParameters.filterParameters);
            } else {
                self.viewModel.LoadPersonToPerVehicleNormalAccessViewObjectCollection();
            }
        };

        this.onPersonToPerVehicleNormalAccessViewFilterViewModelSearch = function () {
            var predicateAndParameters = self.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel.getFilterPredicateAndParameters(self.viewModel.baseFilterParametersCount);
            if (predicateAndParameters.filterPredicate !== null && predicateAndParameters.filterPredicate !== "") {
                self.addFilterPredicateAndParameters(predicateAndParameters.filterPredicate, predicateAndParameters.filterParameters);
            } else {
                self.clearFilterPredicate();
            }
        };

        this.onPersonToPerVehicleNormalAccessViewFilterViewModelClear = function () {
            self.clearFilterPredicate();
        };

        this.OnPersonToPerVehicleNormalAccessViewObjectCollectionCounted = function (count, configuration) {
            self.viewModel.totalCollection(count);
            self.viewModel.totalPageNumber(Math.ceil(count / self.viewModel.pageSize));
            if (self.viewModel.pageNumber() >= self.viewModel.totalPageNumber())
                self.viewModel.pageNumber(self.viewModel.totalPageNumber() > 0 ? self.viewModel.totalPageNumber() - 1 : 0);

            self.LoadPagedPersonToPerVehicleNormalAccessViewObjectCollection(configuration);
        };

        this.LoadPagedPersonToPerVehicleNormalAccessViewObjectCollection = function (configuration) {
            if (!self.viewModel.DataStore)
                return;

            self.viewModel.StatusData.IsBusy(true);

            var configuration = $.extend(configuration || {}, {
                contextId: self.viewModel.contextId,
                pageSize: self.viewModel.pageSize,
                pageNumber: 1 + parseInt(self.viewModel.pageNumber()), // model and widget is 0-based, so we convert
                successHandler: self.onPageDataLoaded,
                errorHandler: self.viewModel.ShowError
            });

            if (self.viewModel.filterPredicate) {
                configuration.filterPredicate = self.viewModel.filterPredicate;
            }

            if (self.viewModel.filterParameters) {
                configuration.filterParameters = self.viewModel.filterParameters;
            }

            if (self.viewModel.sortOrder()) {
                configuration.sortOrder = self.viewModel.sortOrder();
            }

            if (self.viewModel.sortColumnName()) {
                configuration.sortColumn = self.viewModel.sortColumnName();
            }

            if (self.viewModel.include) {
                configuration.include = self.viewModel.include;
            }

            self.viewModel.DataStore.LoadObjectCollection(configuration);
        };

        // Add the paged load method to the viewModel for external access
        this.viewModel.LoadPagedPersonToPerVehicleNormalAccessViewObjectCollection = this.LoadPagedPersonToPerVehicleNormalAccessViewObjectCollection;

        this.viewModel.setPageNumber = function (pageNumber) {
            self.viewModel.ignorePageChange = true;
            self.viewModel.pageNumber(pageNumber);
            self.viewModel.ignorePageChange = false;
        };

        // Method to capture the original state of the current page
        this.captureOriginalPageState = function () {
            if (!self.viewModel.viewModelCollection) return;

            self.originalPageState = {};
            for (var i = 0; i < self.viewModel.viewModelCollection().length; i++) {
                var item = self.viewModel.viewModelCollection()[i];
                var currentObject = item.CurrentObject();
                if (currentObject && currentObject.Data) {
                    var key = self.getItemKey(currentObject);
                    self.originalPageState[key] = currentObject.Data.HasAccess();
                }
            }
        };

        // Method to get a unique key for an item
        this.getItemKey = function (currentObject) {
            if (!currentObject || !currentObject.Data) return null;
            return currentObject.Data.PersonId();
        };

        // Method to check if the current page has changes
        this.hasPageChanges = function () {
            if (!self.viewModel.viewModelCollection || !self.originalPageState) return false;

            for (var i = 0; i < self.viewModel.viewModelCollection().length; i++) {
                var item = self.viewModel.viewModelCollection()[i];
                var currentObject = item.CurrentObject();
                if (currentObject && currentObject.Data) {
                    var key = self.getItemKey(currentObject);
                    var originalState = self.originalPageState[key];
                    var currentState = currentObject.Data.HasAccess();

                    if (originalState !== currentState) {
                        return true;
                    }
                }
            }
            return false;
        };

        // Method to show navigation confirmation dialog
        this.showNavigationConfirmation = function () {
            var message = "Navigating to another page will lose your current changes. Do you want to continue without saving?";
            var title = "Unsaved Changes";

            if (self.viewModel.controller && self.viewModel.controller.applicationController) {
                self.viewModel.controller.applicationController.showConfirmPopup(
                    self.viewModel,
                    message,
                    title,
                    self.onNavigationConfirmed,
                    self.viewModel.contextId
                );
            }
        };

        // Callback for navigation confirmation
        this.onNavigationConfirmed = function (result) {
            if (result === true && self.pendingPageNumber !== null) {
                // User confirmed, proceed with navigation
                self.viewModel.pageNumber(self.pendingPageNumber);
                self.pendingPageNumber = null;
            } else {
                // User cancelled, clear pending navigation
                self.pendingPageNumber = null;
            }
        };

        // Custom success handler that captures original state after data is loaded
        this.onPageDataLoaded = function (objectsLoaded, configuration) {
            // Call the original success handler first
            self.viewModel.OnPersonToPerVehicleNormalAccessViewObjectCollectionLoaded(objectsLoaded);

            // Capture the original state after a short delay to ensure all bindings are updated
            setTimeout(function () {
                self.captureOriginalPageState();
            }, 100);
        };

        this.initialize = function () {
            // Add IsFilterVisible property to StatusData
            self.viewModel.StatusData.IsFilterVisible = ko.observable(true);

            // Expose filter properties at the list viewmodel root so the generated filter template
            // can resolve tokens like {DATABINDROOT}commands.searchCommand in the list context

            // Don't overwrite existing commands, add filter commands to them
            if (!self.viewModel.commands) {
                self.viewModel.commands = {};
            }

            // Add filter commands to the existing commands object
            if (self.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel.commands) {
                self.viewModel.commands.searchCommand = self.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel.commands.searchCommand;
                self.viewModel.commands.clearCommand = self.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel.commands.clearCommand;
            }

            self.viewModel.filterData = self.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel.filterData;
            self.viewModel.statusData = self.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel.statusData;
            self.viewModel.HasAccessValues = self.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel.HasAccessValues;

            // Store reference to original load method now that viewModel is initialized
            self.originalLoadMethod = self.viewModel.LoadPersonToPerVehicleNormalAccessViewObjectCollection;

            // Override the original load method to add pagination
            self.viewModel.LoadPersonToPerVehicleNormalAccessViewObjectCollection = function (configuration) {
                self.viewModel.StatusData.IsBusy(true);

                if (!configuration) {
                    configuration = {};
                }

                configuration.successHandler = self.OnPersonToPerVehicleNormalAccessViewObjectCollectionCounted;
                configuration.contextId = self.viewModel.contextId;

                if (self.viewModel.include) {
                    configuration.include = self.viewModel.include;
                }

                if (self.viewModel.filterPredicate) {
                    configuration.filterPredicate = self.viewModel.filterPredicate;
                }

                if (self.viewModel.filterParameters) {
                    configuration.filterParameters = self.viewModel.filterParameters;
                }

                self.viewModel.DataStore.CountObjects(configuration);
            };

            // Subscribe to page number changes
            self.viewModel.subscriptions.push(self.viewModel.pageNumber.subscribe(
                function (iPageNumber) {
                    if (!self.viewModel.ignorePageChange) {
                        self.viewModel.LoadPersonToPerVehicleNormalAccessViewObjectCollection();
                    }
                }
            ));

            // Subscribe to filter events
            self.viewModel.subscriptions.push(self.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel.events.onSearch.subscribe(function (newValue) {
                self.onPersonToPerVehicleNormalAccessViewFilterViewModelSearch();
            }));
            self.viewModel.subscriptions.push(self.viewModel.PersonToPerVehicleNormalAccessViewFilterViewModel.events.onClear.subscribe(function (newValue) {
                self.onPersonToPerVehicleNormalAccessViewFilterViewModelClear();
            }));
        };

        this.release = function () {
            // Clean up any custom subscriptions or resources if needed
        };
    };
}());