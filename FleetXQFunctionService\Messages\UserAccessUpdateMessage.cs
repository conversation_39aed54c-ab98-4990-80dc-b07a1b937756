using System;

namespace FleetXQFunctionService.Messages
{
    /// <summary>
    /// Message class for user access update operations sent to Azure Service Bus
    /// Contains all the necessary data to process user access updates asynchronously
    /// </summary>
    public class UserAccessUpdateMessage
    {
        /// <summary>
        /// The person ID for whom access is being updated
        /// </summary>
        public Guid PersonId { get; set; }

        /// <summary>
        /// Customer ID for multi-tenant isolation
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// Site vehicle access updates (serialized as JSON)
        /// </summary>
        public string? PersonToSiteAccessesJson { get; set; }

        /// <summary>
        /// Department vehicle access updates (serialized as JSO<PERSON>)
        /// </summary>
        public string? PersonToDepartmentAccessesJson { get; set; }

        /// <summary>
        /// Model vehicle access updates (serialized as JSON)
        /// </summary>
        public string? PersonToModelAccessesJson { get; set; }

        /// <summary>
        /// Per-vehicle access updates (serialized as <PERSON><PERSON><PERSON>)
        /// </summary>
        public string? PersonToVehicleAccessesJson { get; set; }

        /// <summary>
        /// Timestamp when the message was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// User ID who initiated the access update
        /// </summary>
        public Guid? InitiatedByUserId { get; set; }

        /// <summary>
        /// Optional correlation ID for tracking related operations
        /// </summary>
        public string? CorrelationId { get; set; }

        /// <summary>
        /// Priority level for processing (Normal, High, Critical)
        /// </summary>
        public string Priority { get; set; } = "Normal";

        /// <summary>
        /// Permission level being updated (Master=1, NormalDriver=3)
        /// This ensures the queue processor targets the correct permission level
        /// </summary>
        public int PermissionLevel { get; set; } = 3;

        /// <summary>
        /// Whether to cascade access permissions when adding access to parent entities
        /// If true, adding site access will also add department, model, and vehicle access
        /// If false, only the specific access level will be updated
        /// </summary>
        public bool CascadeAddPermission { get; set; } = true;
    }
}