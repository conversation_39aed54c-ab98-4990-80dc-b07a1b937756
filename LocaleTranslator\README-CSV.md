### LocaleTranslator CSV Export and Import

This tool supports both automatic and manual CSV exports. The CSV is meant for human review and round-trip import.

### When it runs
- Automatic: after successful `translate` command completion
- Manual: via `export-csv` command
- Not triggered by other commands (e.g., `status`)

### How CSV is generated
1. **Source**: Reads JSON files from the source language directory
   - During `translate`: the `--source` directory you provide
   - During manual `export-csv`: auto-detected as a sibling `english` or `default` directory next to the target language directory
2. **Target**: Pairs with corresponding translated files from the target language directory  
3. **Flattening**: Converts nested JSON structures to flat key-value pairs
   - `{ "user": { "name": "<PERSON>" } }` becomes `user.name = "<PERSON>"`
   - Arrays become `key[0]`, `key[1]`, etc.
4. **Extraction**: Creates one CSV row per string value with:
   - Original text, translated text, file path, and key path
5. **Output**: Writes to timestamped CSV in `exports/` folder

### Output
- Folder: `LocaleTranslator/exports/`
- File name: `translations_{targetLanguage}_{yyyyMMdd_HHmmss}.csv`
  - Example: `translations_french_20250811_153045.csv`

### CSV format
Columns (RFC 4180 compliant, UTF-8 with BOM):
1. `SourceText`
2. `TranslatedText`
3. `SourceLanguage` (derived from source folder name)
4. `TargetLanguage` (from `--language`)
5. `FilePath` (relative to source root)
6. `KeyPath` (dot/bracket path to the JSON value)

Notes:
- Proper quoting/escaping for commas, quotes, and newlines
- String values only (non-string JSON values are skipped)

### 2. Automatic CSV Export After New Translation Completion

Automatically generates a CSV after a successful `translate` run. It pairs the `--source` directory with the newly generated files in `--target` from that run (no extra input required).

```bash
dotnet run -- translate \
  --source "D:\CODEZ\workz\fleetxq\GeneratedCode\WebApplicationLayer\wwwroot\locales\english" \
  --target "D:\CODEZ\workz\fleetxq\GeneratedCode\WebApplicationLayer\wwwroot\locales\french" \
  --language "French" --verbose
```

After completion, check the `exports` folder for the CSV.

### 1. Manual CSV Export from Existing Translations
Export an existing language directory to CSV without running translation. Useful when you already have translated JSON files and want a reviewable/editable CSV.

By language folder name (auto-resolves typical locales path):
```bash
dotnet run -- export-csv --export-language "russian" --verbose
```

By full path:
```bash
dotnet run -- export-csv --export-language "D:\\CODEZ\\workz\\fleetxq\\GeneratedCode\\WebApplicationLayer\\wwwroot\\locales\\russian"
```

Behavior:
- Determines source as a sibling `english` (or `default`) directory to the provided target language directory
- Produces `exports/translations_{language}_{timestamp}.csv`

### Review workflow (suggested)
- Open the CSV in Excel or similar
- Filter/sort by `FilePath` or `KeyPath`
- Update `TranslatedText` as needed
- Save as CSV (keep UTF-8 with BOM, same headers)

### Scope and safety
- The export does not modify JSON files
- Existing translation flow is unchanged; CSV is an additional output

### 3. CSV Import and Update of JSON files Functionality

Create/update JSON translation files from CSV data (round-trip editing workflow).

**Command:**
```bash
dotnet run -- import-csv \
  --csv-file "./exports/translations_french_20250811_153045.csv" \
  --target "..\GeneratedCode\WebApplicationLayer\wwwroot\locales\french" \
  --verbose
```

**What it does:**
1. **Reads CSV**: Parses rows with proper handling of quotes and escaping
2. **Validates headers**: Requires columns `SourceText, TranslatedText, SourceLanguage, TargetLanguage, FilePath, KeyPath`
3. **Groups by file**: Organizes entries by `FilePath`
4. **Updates JSON in place**: If a target JSON exists, only specified values are updated, preserving structure and other keys
5. **Creates files when missing**: If a file does not exist, it is created
6. **Rebuilds structure**: Converts flat `KeyPath` back to nested objects/arrays
   - `user.name` → `{ "user": { "name": "value" } }`
   - `items[0].title` → `{ "items": [{ "title": "value" }] }`
7. **Backup**: Creates `.backup_yyyyMMdd_HHmmss` before overwriting when enabled

**Options:**
- `--csv-file` / `-c`: Path to CSV file (required)
- `--target` / `-t`: Target directory for JSON files (required)  
- `--backup` / `-b`: Create backup files (default: true)
- `--verbose` / `-v`: Enable detailed logging

**Export command options:**
- `--export-language`: Language folder name or full path (required)
- `--verbose` / `-v`: Enable detailed logging

**Use cases:**
- **Human review workflow**: Export → Edit in Excel → Import back
- **Translation memory**: Use existing translations to create new language files
- **Bulk corrections**: Fix multiple translations at once via spreadsheet


