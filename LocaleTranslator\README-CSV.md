### LocaleTranslator CSV Export (Quick Guide)

This tool automatically exports translations to CSV during translate runs. The CSV is meant for human review and can be used for future import workflows.

### When it runs
- Only on `translate` command runs
- Not triggered by other commands (e.g., `status`)

### Output
- Folder: `LocaleTranslator/exports/`
- File name: `translations_{targetLanguage}_{yyyyMMdd_HHmmss}.csv`
  - Example: `translations_french_20250811_153045.csv`

### CSV format
Columns (RFC 4180 compliant, UTF-8 with BOM):
1. `SourceText`
2. `TranslatedText`
3. `SourceLanguage` (derived from source folder name)
4. `TargetLanguage` (from `--language`)
5. `FilePath` (relative to source root)
6. `KeyPath` (dot/bracket path to the JSON value)

Notes:
- Proper quoting/escaping for commas, quotes, and newlines
- String values only (non-string JSON values are skipped)

### How to run

```bash
dotnet run -- translate \
  --source "D:\CODEZ\workz\fleetxq\GeneratedCode\WebApplicationLayer\wwwroot\locales\english" \
  --target "D:\CODEZ\workz\fleetxq\GeneratedCode\WebApplicationLayer\wwwroot\locales\french" \
  --language "French" --verbose
```

After completion, check the `exports` folder for the CSV.

### Review workflow (suggested)
- Open the CSV in Excel or similar
- Filter/sort by `FilePath` or `KeyPath`
- Update `TranslatedText` as needed
- Save as CSV (keep UTF-8 with BOM, same headers)

### Scope and safety
- The export does not modify JSON files
- Existing translation flow is unchanged; CSV is an additional output

### Import (coming next)
- CSV import will map `FilePath` + `KeyPath` to update JSON values
- Keep the header row and column order unchanged for future import compatibility


