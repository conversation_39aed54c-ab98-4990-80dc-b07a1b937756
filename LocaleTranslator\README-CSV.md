### LocaleTranslator CSV Export (Quick Guide)

This tool automatically exports translations to CSV during translate runs. The CSV is meant for human review and can be used for future import workflows.

### When it runs
- Only on `translate` command runs
- Not triggered by other commands (e.g., `status`)
- Automatically runs after successful translation completion

### How CSV is generated
1. **Source**: Reads JSON files from `--source` directory
2. **Target**: Pairs with corresponding translated files from `--target` directory  
3. **Flattening**: Converts nested JSON structures to flat key-value pairs
   - `{ "user": { "name": "<PERSON>" } }` becomes `user.name = "<PERSON>"`
   - Arrays become `key[0]`, `key[1]`, etc.
4. **Extraction**: Creates one CSV row per string value with:
   - Original text, translated text, file path, and key path
5. **Output**: Writes to timestamped CSV in `exports/` folder

### Output
- Folder: `LocaleTranslator/exports/`
- File name: `translations_{targetLanguage}_{yyyyMMdd_HHmmss}.csv`
  - Example: `translations_french_20250811_153045.csv`

### CSV format
Columns (RFC 4180 compliant, UTF-8 with BOM):
1. `SourceText`
2. `TranslatedText`
3. `SourceLanguage` (derived from source folder name)
4. `TargetLanguage` (from `--language`)
5. `FilePath` (relative to source root)
6. `KeyPath` (dot/bracket path to the JSON value)

Notes:
- Proper quoting/escaping for commas, quotes, and newlines
- String values only (non-string JSON values are skipped)

### How to run

```bash
dotnet run -- translate \
  --source "D:\CODEZ\workz\fleetxq\GeneratedCode\WebApplicationLayer\wwwroot\locales\english" \
  --target "D:\CODEZ\workz\fleetxq\GeneratedCode\WebApplicationLayer\wwwroot\locales\french" \
  --language "French" --verbose
```

After completion, check the `exports` folder for the CSV.

### Review workflow (suggested)
- Open the CSV in Excel or similar
- Filter/sort by `FilePath` or `KeyPath`
- Update `TranslatedText` as needed
- Save as CSV (keep UTF-8 with BOM, same headers)

### Scope and safety
- The export does not modify JSON files
- Existing translation flow is unchanged; CSV is an additional output

### CSV Import Feature

You can now create/update JSON translation files from CSV data:

**Command:**
```bash
dotnet run -- import-csv \
  --csv-file "./exports/translations_french_20250811_153045.csv" \
  --target "..\GeneratedCode\WebApplicationLayer\wwwroot\locales\french" \
  --verbose
```

**What it does:**
1. **Reads CSV**: Parses the CSV file with proper handling of quotes and escaping
2. **Groups by file**: Organizes entries by `FilePath` column
3. **Rebuilds JSON**: Converts flat key paths back to nested JSON structures
   - `user.name` becomes `{ "user": { "name": "value" } }`
   - `items[0].title` becomes `{ "items": [{ "title": "value" }] }`
4. **Creates files**: Writes JSON files to target directory with proper structure
5. **Backup**: Automatically creates `.backup_yyyyMMdd_HHmmss` files before overwriting

**Options:**
- `--csv-file` / `-c`: Path to CSV file (required)
- `--target` / `-t`: Target directory for JSON files (required)  
- `--backup` / `-b`: Create backup files (default: true)
- `--verbose` / `-v`: Enable detailed logging

**Use cases:**
- **Human review workflow**: Export → Edit in Excel → Import back
- **Translation memory**: Use existing translations to create new language files
- **Bulk corrections**: Fix multiple translations at once via spreadsheet


